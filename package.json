{"name": "wx_hub", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build"}, "dependencies": {"all": "0.0.0", "axios": "^0.19.0", "core-js": "^3.4.4", "dayjs": "^1.11.13", "encryptlong": "^3.1.4", "jsencrypt": "^3.0.0-rc.1", "nprogress": "^0.2.0", "register-service-worker": "^1.6.2", "vant": "^2.10.13", "vue": "^2.6.10", "vue-pdf": "^4.2.0", "vue-router": "^3.1.3", "vuex": "^3.1.2", "weixin-jsapi": "^1.1.0"}, "devDependencies": {"@vue/cli-plugin-babel": "^4.1.0", "@vue/cli-plugin-pwa": "^4.1.0", "@vue/cli-plugin-router": "^4.1.0", "@vue/cli-plugin-vuex": "^4.1.0", "@vue/cli-service": "^4.5.4", "crypto-js": "^4.0.0", "jquery": "^3.4.1", "sass": "^1.26.5", "sass-loader": "^8.0.0", "vue-template-compiler": "^2.6.10"}}