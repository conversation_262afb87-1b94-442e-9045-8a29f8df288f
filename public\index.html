<!--
 * @Author: 536584936 <EMAIL>
 * @Date: 2024-12-23 14:17:34
 * @LastEditors: 536584936 <EMAIL>
 * @LastEditTime: 2024-12-26 13:43:35
 * @FilePath: \WxExaminationHub_DaLiang\public\index.html
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta http-equiv="pragram" content="no-cache">
    <meta http-equiv="cache-control" content="no-cache, no-store, must-revalidate">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,user-scalable=0">
    <link rel="icon" href="<%= BASE_URL %>favicon.ico">
    <title>郑州蓝天体检中心</title>
  </head>
  <body>
    <noscript>
      <strong>We're sorry but wx_hub doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
    </noscript>
    <div id="app"></div>
    <!-- built files will be auto injected -->
  </body>
</html>

<script type="text/javascript">
  (function () {
       if (typeof WeixinJSBridge == "object" && typeof WeixinJSBridge.invoke == "function") {
           handleFontSize();
       } else {
           if (document.addEventListener) {

               document.addEventListener("WeixinJSBridgeReady", handleFontSize, false);

           } else if (document.attachEvent) {

               document.attachEvent("WeixinJSBridgeReady", handleFontSize);

               document.attachEvent("onWeixinJSBridgeReady", handleFontSize);
           }
       }
       function handleFontSize() {

           // 设置网页字体为默认大小
           WeixinJSBridge.invoke('setFontSizeCallback', {

               'fontSize': 0

           });
           // 重写设置网页字体大小的事件
           WeixinJSBridge.on('menu:setfont', function () {

               WeixinJSBridge.invoke('setFontSizeCallback', {

                   'fontSize': 0

               });

           });

       }
    })();
    //限定微信内打开
    // var ua = navigator.userAgent.toLowerCase();
    //   var isWeixin = ua.indexOf('micromessenger') != -1;
    //   var isAndroid = ua.indexOf('android') != -1;
    //   var isIos = (ua.indexOf('iphone') != -1) || (ua.indexOf('ipad') != -1);
    //   var ismobile=ua.indexOf('mobile')!= -1;
    //   if (!isWeixin || !ismobile) {
    //     document.head.innerHTML = '<title>抱歉，出错了</title><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=0"><link rel="stylesheet" type="text/css" href="https://res.wx.qq.com/open/libs/weui/0.4.1/weui.css">';
    //     document.body.innerHTML = '<div class="weui_msg"><div class="weui_icon_area"><i class="weui_icon_info weui_icon_msg"></i></div><div class="weui_text_area"><h4 class="weui_msg_title">请在微信客户端打开链接</h4></div></div>';
    //   }
</script>
