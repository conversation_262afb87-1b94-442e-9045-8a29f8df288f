<template>
  <div id="app">
    
    <router-view/>
  </div>
</template>


<script>
import {storage,ajax} from './common'
import apiUrls from './config/apiUrls'
import {Toast} from 'vant'
import {authService} from './service'
import qs from 'qs'
import {getTokenApi} from "./common/getTokenApiHttp"
export default {
  components: {
    // HeaderBar
  },
    data() {
    return {
    }
  },
    created(){
      getTokenApi("Zhengzhou_blue_sky","F3EDCE24EF64911D761EAF6A2E5DAB67");
    },
    mounted(){
      //  var token= authService.getToken();
      //  if(token==null)
      //  {
      //    //去获取token
      //    this.apiGetToken();
      //  }
    },
  methods:{
    //微信授权登录
      // apiGetToken(){
      //    var that=this;
      //     var data = {
      //       //认证类型 
      //       grant_type: 'password',
      //       client_id: 'clientanduser',
      //       client_secret:'secret',
      //       username: "m1",
      //       password: "password",
      //       scope:"api1"
      //     }
      //     //注意这里需要使用fromdata的请求
      //   ajax.post("/connect/token", qs.stringify(data),{headers:{ 'content-type':'application/x-www-form-urlencoded'}}).then(r=>{
      //     r=r.data;
      //     // console.log(r);
      //     authService.setToken(r); 
      //   }).catch(e=>{
      //     console.log(e);
      //   });

      // },
      apiGetToken() {
      let openid = storage.cookie.get("openid")
      if (openid && openid.length > 15) {
        var that = this;
        var data = {
          openid: openid
        };
        ajax
          .post(apiUrls.GetToken, data, { nocrypt: true })
          .then((r) => {
            r = r.data;
            if (!r.success) {
              Toast(r.returnMsg);
              return;
            }
            //设置token
            authService.setToken(r.returnData); 
            return;
          })
          .catch((e) => {
            return;
          });
      }else{

      }

    },
  }

};
</script>


<style lang="scss">

body {
    background: #efefefe8;
}
#app {
  height: 100%;
  -webkit-overflow-scrolling: touch;
}

</style>
