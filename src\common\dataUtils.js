export const dataUtils = {
    //手机号码验证
    isTel(tel) {
        var reg = /^1[3|4|5|6|7|8|9][0-9]{9}$/;
        if (!reg.test(tel)) {
            return "手机号码格式错误";
        }
        return true;
    },
    //身份证判断
    isCardID(sId) {
        var aCity = { 11: "北京", 12: "天津", 13: "河北", 14: "山西", 15: "内蒙古", 21: "辽宁", 22: "吉林", 23: "黑龙江", 31: "上海", 32: "江苏", 33: "浙江", 34: "安徽", 35: "福建", 36: "江西", 37: "山东", 41: "河南", 42: "湖北", 43: "湖南", 44: "广东", 45: "广西", 46: "海南", 50: "重庆", 51: "四川", 52: "贵州", 53: "云南", 54: "西藏", 61: "陕西", 62: "甘肃", 63: "青海", 64: "宁夏", 65: "新疆", 71: "台湾", 81: "香港", 82: "澳门", 91: "国外" };
        var iSum = 0;
        var info = "";
        if (!/^\d{17}(\d|x)$/i.test(sId)) return "你输入的身份证长度或格式错误";
        sId = sId.replace(/x$/i, "a");
        if (aCity[parseInt(sId.substr(0, 2))] == null) return "你的身份证地区非法";
        var sBirthday = sId.substr(6, 4) + "-" + Number(sId.substr(10, 2)) + "-" + Number(sId.substr(12, 2));
        var d = new Date(sBirthday.replace(/-/g, "/"));
        if (sBirthday != (d.getFullYear() + "-" + (d.getMonth() + 1) + "-" + d.getDate())) return "身份证上的出生日期非法";
        for (var i = 17; i >= 0; i--) iSum += (Math.pow(2, i) % 11) * parseInt(sId.charAt(17 - i), 11);
        if (iSum % 11 != 1) return "你输入的身份证号非法";
        //aCity[parseInt(sId.substr(0,2))]+","+sBirthday+","+(sId.substr(16,1)%2?"男":"女");//此次还可以判断出输入的身份证号的人性别
        return true;
    },
    NowDate() {
        // 2020/2/13 0:00:00
        var date = new Date();
        var Day = date.getFullYear() + '-' + ((date.getMonth() + 1) < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-' + date.getDate() + " " + date.getHours() + ":" + date.getMinutes() + ":" + date.getSeconds();
        return Day;
    },
    //计算金额
    priceSum(a, b, c) {
        let arg1 = 0;
        let arg2 = 0;
        let pr = 0;
        //增大100倍,获得整数
        let a1 = a.toFixed(2).split(".");
        if (a1[0] === "0") {
            if (a1[1] === "00") {
                arg1 = 0;
            } else {
                arg1 = Number(a1[1])
            }
        } else {
            arg1 = Number(a1[0] + a1[1])
        }

        let b1 = b.toFixed(2).split(".");
        if (b1[0] === "0") {
            if (b1[1] === "00") {
                arg2 = 0;
            } else {
                arg2 = Number(b1[1])
            }
        } else {
            arg2 = Number(b1[0] + b1[1])
        }

        if (c == "T") {
            pr = arg1 - arg2;
        } else {
            pr = arg1 + arg2;
        }
        if (pr>0) {
            let r = 0;
            // let aac = pr / 100;
            let aac = pr.toString();
            // console.log("aac",aac);
            if (aac.length > 2) {
                let n = aac.slice(-2);
                let m = aac.slice(0, -2);
                r = Number(m + '.' + n);
                return r;
            } else if (aac.length == 2) {
                r = Number('0.' + aac);
            } else if (aac.length == 1) {
                r = Number('0.0' + aac);
            }
            return r;
        } else {
            return 0;
        }
    },


}