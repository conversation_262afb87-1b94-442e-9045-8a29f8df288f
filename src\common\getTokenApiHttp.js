let isToken = 0;
import { storage, ajax } from "../common/index";
import apiUrls from "../config/apiUrls";
import { authService } from "../service/authService";

//微信授权登录
const apiGetToken = function(username, password) {
  var that = this;
  var data = {
    //认证类型
    grant_type: "password",
    client_id: "clientanduser",
    client_secret: "secret",
    name:username,
    password,
    // username: "m1sz<PERSON><PERSON>",
    // password: "szzyypassword",
    scope: "api1",
  };
  // var _0x3498 = ["clientanduser", "api1", "password"];
  // var _0x54cc = function(_0x34982a, _0x54cceb) {
  //   _0x34982a = _0x34982a - 0x0;
  //   var _0x1a8942 = _0x3498[_0x34982a];
  //   return _0x1a8942;
  // };
  // var data = {
  //   grant_type: _0x54cc("0x2"),
  //   client_id: _0x54cc("0x0"),
  //   client_secret: "secret",
  //   username: "m1sz<PERSON><PERSON>",
  //   password: "szzyypassword",
  //   scope: _0x54cc("0x1"),
  // };

  //注意这里需要使用fromdata的请求
  // ajax.post("/connect/token", qs.stringify(data),{headers:{ 'content-type':'application/x-www-form-urlencoded'}}).then(r=>{
  ajax
    .post(apiUrls.GetToken, data, { nocrypt: true })
    .then((r) => {
      r = r.data.returnData;
      // console.log(r);
      authService.setToken(r);
    })
    .catch((e) => {
      console.log(e);
    });
};

export const getTokenApi = function(username, password) {
  isToken++;
  if (isToken == 1) {
    var token = authService.getToken();
    if (token == null) {
      //去获取token
      apiGetToken(username, password);
    }
  }
};
