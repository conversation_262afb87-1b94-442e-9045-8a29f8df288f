class WebStorage {
    constructor(instance) {
      this.instance = instance;
    }

    get(key) {
      return this.instance.getItem(key);
      //return JSON.parse(this.instance.getItem(key));
    }

    set(key, value) {
      // if(typeof value === 'string'){
      //   this.instance.setItem(key, value);
      // }else{
      //   this.instance.setItem(key, JSON.stringify(value));
      // }
      this.instance.setItem(key, value);
    }

    delete(key) {
      this.instance.removeItem(key);
    }

    clear() {
      this.instance.clear();
    }
  }

  const memoryMap = new Map();

  export const storage = {
    local: new WebStorage(localStorage),
    session: new WebStorage(sessionStorage),
    memory: {
      get(key) {
        return memoryMap.get(key);
      },
      set(key, value) {
        memoryMap.set(key, value);
      },
      delete(key) {
        memoryMap.delete(key);
      },
      clear() {
        memoryMap.clear();
      }
    },
    cookie: {
      get(key) {
        let cookies = document.cookie.split(';');
        for (let cookie of cookies) {
          let cArr = cookie.split('=');
          if (cArr[0].trim() === key) {
            return  decodeURIComponent(cArr[1]);
          }
        }
        return null;
      },
      set(key,value,times) {
        var exdate=new Date();
        times=times||(60*50*2);
        exdate.setTime(exdate.getTime()+(1000*times));
        document.cookie=key+ "=" +encodeURIComponent(value)+
        ((times==null) ? "" : ";expires="+exdate.toGMTString())
      },
      delete(key)
      {
          var date = new Date(); 
          date.setTime(date.getTime() - 10000); 
          document.cookie = key + "=a; expires=" + date.toGMTString(); 
      },
      clear()
      {
        var date=new Date();
         date.setTime(date.getTime()-10000);
        var keys=document.cookie.match(/[^ =;]+(?=\=)/g);
        //console.log("需要删除的cookie名字："+keys);
         if (keys) {
             for (var i =  keys.length; i--;)
                document.cookie=keys[i]+"=0; expire="+date.toGMTString()+"; path=/";
         }
      }
    }
  };
