﻿//基础工具
import Vue from 'vue'
import CryptoJS from 'crypto-js';
import JSEncrypt from 'jsencrypt'


export const toolsUtils = {

  //暂停线程时间
  sleep(numberMillis) {
    var now = new Date();
    var exitTime = now.getTime() + numberMillis;
    while (true) {
      now = new Date();
      if (now.getTime() > exitTime)
        return;
    }
  },
 
  getNow() {
    var now = new Date();
    var data = now.getFullYear() + "-" + (now.getMonth() + 1) + "-" + now.getDate() + "  " + now.getHours() + ":" + now.getMinutes()
    return data;
  },
  //加密
  encrypt(word, keyStr) {
    keyStr = keyStr ? keyStr : 'Q8sc5vG<M4A>dk2L';
    // console.log(word+'-'+keyStr);
    var key = CryptoJS.enc.Utf8.parse(keyStr); //Latin1 w8m31+Yy/Nw6thPsMpO5fg==
    var srcs = CryptoJS.enc.Utf8.parse(word);
    var encrypted = CryptoJS.AES.encrypt(srcs, key, {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7
    });
    return encrypted.toString();
  },
  //解密
  decrypt(word, keyStr) {
    keyStr = keyStr ? keyStr : 'Q8sc5vG<M4A>dk2L';
    // console.log(word+'-'+keyStr);
    var key = CryptoJS.enc.Utf8.parse(keyStr); //Latin1 w8m31+Yy/Nw6thPsMpO5fg==
    var decrypt = CryptoJS.AES.decrypt(word, key, {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7
    });
    return CryptoJS.enc.Utf8.stringify(decrypt).toString();
  }
  ,
  OpenEnc(word, keyStr) {
    keyStr = keyStr ? keyStr : '0123456789abcdef';
    // console.log(word+'-'+keyStr);
    var key = CryptoJS.enc.Utf8.parse(keyStr); //Latin1 w8m31+Yy/Nw6thPsMpO5fg==
    var srcs = CryptoJS.enc.Utf8.parse(word);
    var encrypted = CryptoJS.AES.encrypt(srcs, key, {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7
    });
    // let ret=encrypted.toString().replaceAll("a","!").replaceAll("=","a");
    return encrypted.toString();
  },
  Rsaencrypt(word, publicKey) {

    let encryptor = new JSEncrypt() // 新建JSEncrypt对象 
    publicKey = publicKey ? keyStr :`MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDYWlmjLxzhU6LC1KZWbfuqwfsi
    27ZJIUlAeyWYe/ggnn3XeiVRNB2t5Vrfpxz8/OqE9y2bBpnhZGjjGOXK+TMapCE/
    WgXsnQy2jCgdG/4ucH9ZCBv+nfqaxz1XYnjoO3j4NXhfPcGOFb+HR1x7n8HiFSYX
    sfA1n751yL17LZyRuwIDAQAB`  //公秘钥
   
    encryptor.setPublicKey(publicKey) // 设置公钥
    
    let rsaPassWord = encryptor.encryptLong(word) // 对需要加密的数据进行加密
    return rsaPassWord;
  }
  ,
  // RSA分段加密，支持中文
  RsaEncryptUnicodeLong(string, publicKey) {
    let defaultPublicKey = `MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAsT2DEK56Xaojch9vhkbI
    F19eU8kY21EpZw5dXiTYvRvLoUC2F0jRJASPyvVUmJQmAwfnoqafRuYSxXqCzdW+
    ON51u5peeOM2N9tpASJ7AnGK05/VgY/gdInoYKFJsXzEftIGLNsiy9h4StyqvdnY
    megHk1JMtEuriBo78RY5JUS08SiyTNXAl4zEyswaZPSq8r2/pCy795TuYgRREORu
    7+Z95XOKVZL5G64SgU0J3sAKs868cPjfk5ZogVC+0tFpM+ZAeIKEhPfFr+XzatUa
    oqFdli/8c85KeejJww2Z3vuCx5S9tczQbbHKx7pTNUan9GgE4I/vO47zq1nkM2xt
    DwIDAQAB`; //公钥

    let encryptor = new JSEncrypt();
    encryptor.setPublicKey(publicKey || defaultPublicKey) // 设置公钥
    var k = encryptor.getKey();
    //根据key所能编码的最大长度来定分段长度。key size - 11：11字节随机padding使每次加密结果都不同。
    var maxLength = ((k.n.bitLength() + 7) >> 3) - 11;
    try {
      var subStr = "",
        encryptedString = "";
      var subStart = 0,
        subEnd = 0;
      var bitLen = 0,
        tmpPoint = 0;
      for (var i = 0, len = string.length; i < len; i++) {
        //js 是使用 Unicode 编码的，每个字符所占用的字节数不同
        var charCode = string.charCodeAt(i);
        if (charCode <= 0x007f) {
          bitLen += 1;
        } else if (charCode <= 0x07ff) {
          bitLen += 2;
        } else if (charCode <= 0xffff) {
          bitLen += 3;
        } else {
          bitLen += 4;
        }
        //字节数到达上限，获取子字符串加密并追加到总字符串后。更新下一个字符串起始位置及字节计算。
        if (bitLen > maxLength) {
          subStr = string.substring(subStart, subEnd)
          encryptedString += k.encrypt(subStr);
          subStart = subEnd;
          bitLen = bitLen - tmpPoint;
        } else {
          subEnd = i;
          tmpPoint = bitLen;
        }
      }
      subStr = string.substring(subStart, len)
      encryptedString += k.encrypt(subStr);
      return this.hex2b64(encryptedString);
    } catch (ex) {
      return false;
    }
  },
  // RSA分段解密，支持中文
  RsaDecryptUnicodeLong(string, privateKey) {
//     let defaultPrivateKey = `MIICWwIBAAKBgH801AwoxqsS/GYmtUJvUEKG+CazWkXtmjmNoJ4ssZQg3aUwomAA
// hBBv5GmYwIilXVBDz5mA8qyRxO+pkjYj+/AZlDZIF1ees17GPL0wHsP1FBdI0ohD
// c7CZFV70K+zTKw2xLG2CCCkR1N9xXDCm+L5Qpzfwv7vu0T7SQD8IJgsDAgMBAAEC
// gYBnpYzNZ4AQrkSXmxx/yCEWQ9D3/5Ujeyj5kgt4NiRu9KSET29OV71Dg1gSLlNa
// Q5sXplkF00poD9HuETXABWvmHVSDueLe8f/eWP/FSYuZVOS4OKdqXd3/pQEbzzsM
// EwFdj312fDk7K8Th9qhDFR4zSW8zCT5tY7p+Y4BLpypygQJBALk9wUp7jWLbLssC
// 07sfbt+x38v0F7AeXRYiXCkM2ok33c4swDQdRs9QRyX79TH3Ibz7iMDbSDG/XmLR
// awXZaMMCQQCvy/+XRB8CJzij31Xvs+5vS/ULad3LFQVB6K9xD2XoQi51caRyRcN9
// xwwS96ybAzX+l0/b75mimO4/QjCIaLDBAkBTLnT+sk6CBrSTeviC/ZF3J9O8LSb5
// 8hiQ2OsTj+8OUSTr8VJ51G+4pm7ckrC/OB9RUo5NM+rOVAXZT/rEDKWrAkEArVAe
// LKBhPbszMQQG8infIOSuslDt88BGjaL8DCfVHTTaHrkqGerDf6YUNkLtbWmt+tBs
// T/WY7t98yurTdDhaQQJAfaeiyZCqGjzWE5BrofGUxr1hjbdxYtdSPk6yiuBOgMnK
// 3eenlnAqi3SZXEVS3gAUBou5s9vygeyibZEHWwaADw==`;//私钥

    let encryptor = new JSEncrypt();
    encryptor.setPrivateKey(privateKey || defaultPrivateKey);
    var k = encryptor.getKey();
    //解密长度=key size.hex2b64结果是每字节每两字符，所以直接*2
    var maxLength = ((k.n.bitLength() + 7) >> 3) * 2;
    try {
      var hexString = this.b64tohex(string);
      var decryptedString = "";
      var rexStr = ".{1," + maxLength + "}";
      var rex = new RegExp(rexStr, 'g');
      var subStrArray = hexString.match(rex);
      if (subStrArray) {
        subStrArray.forEach(function (entry) {
          decryptedString += k.decrypt(entry);
        });
        return decryptedString;
      }
    } catch (ex) {
      return false;
    }
  },
  hex2b64(h) {
    var b64map = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
    var b64pad = "=";
    var i;
    var c;
    var ret = "";
    for (i = 0; i + 3 <= h.length; i += 3) {
      c = parseInt(h.substring(i, i + 3), 16);
      ret += b64map.charAt(c >> 6) + b64map.charAt(c & 63);
    }
    if (i + 1 == h.length) {
      c = parseInt(h.substring(i, i + 1), 16);
      ret += b64map.charAt(c << 2);
    } else if (i + 2 == h.length) {
      c = parseInt(h.substring(i, i + 2), 16);
      ret += b64map.charAt(c >> 2) + b64map.charAt((c & 3) << 4);
    }
    while ((ret.length & 3) > 0) {
      ret += b64pad;
    }
    return ret;
  },
  b64tohex(str) {
    for (var i = 0, bin = atob(str.replace(/[ \r\n]+$/, "")), hex = []; i < bin.length; ++i) {
      var tmp = bin.charCodeAt(i).toString(16);
      if (tmp.length === 1) tmp = "0" + tmp;
      hex[hex.length] = tmp;
    }
    return hex.join("");
  },
  /**
 * 函数防抖 (只执行最后一次点击)
 * @param fn
 * @param delay
 * @returns {Function}
 * @constructor
 */
 Debounce(fn, t){
  let delay = t || 500;
  let timer;
  console.log(fn)
  console.log(typeof fn)
  return function () {
      let args = arguments;
      if(timer){
          clearTimeout(timer);
      }
      timer = setTimeout(() => {
          timer = null;
          fn.apply(this, args);
      }, delay);
  }
}
/**
* 函数节流
* @param fn
* @param interval
* @returns {Function}
* @constructor
*/
,
 Throttle(fn, t){
  let last;
  let timer;
  let interval = t || 500;
  return function () {
      let args = arguments;
      let now = +new Date();
      if (last && now - last < interval) {
          clearTimeout(timer);
          timer = setTimeout(() => {
              last = now;
              fn.apply(this, args);
          }, interval);
      } else {
          last = now;
          fn.apply(this, args);
      }
  }
}

}
