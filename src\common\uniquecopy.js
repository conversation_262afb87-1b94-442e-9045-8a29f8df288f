import CryptoJS from 'crypto-js';

export const unique = {

      getunique() {
		var canvas = document.createElement('canvas');
		var ctx = canvas.getContext('2d');
		var txt = 'micholemicholemichole';
		ctx.textBaseline = "top";
		ctx.font = "14px 'Arial'";
		ctx.textBaseline = "tencent";
		ctx.fillStyle = "#f60";
		ctx.fillRect(125, 1, 70, 30);
		ctx.fillStyle = "#069";
		ctx.fillText(txt, 2, 15);
		ctx.fillStyle = "rgba(102, 204, 0, 0.7)";
		ctx.fillText(txt, 4, 17);
 
		var b64 = canvas.toDataURL().replace("data:image/png;base64,", "");
		var bin = atob(b64);
		var crc = this.bin2hex(bin.slice(-16, -12));
        //var crc = bin.slice(-16,-12);
		return CryptoJS.MD5(crc).toString().toUpperCase();
	},
    bin2hex(str) {
		var result = "";
		for (var i = 0; i < str.length; i++) {
			result += this.int16_to_hex(str.charCodeAt(i));
		}
		return result;
	},
	int16_to_hex(i) {
		var result = i.toString(16);
		var j = 0;
		while (j + result.length < 4) {
			result = "0" + result;
			j++;
		}
		return result;
	}
   
}
