<template>
  <div class="health-questionnaire">
    <div class="questionnaire-section">
      <!-- 生活习惯 -->
      <div class="section-title">生活习惯</div>
      <div class="question-group">
        <div class="question-item">
          <span class="question-label">吸烟史：</span>
          <van-radio-group v-model="form.smoking" direction="horizontal">
            <van-radio name="有">有</van-radio>
            <van-radio name="无">无</van-radio>
          </van-radio-group>
        </div>
        <div class="question-item">
          <span class="question-label">饮酒史：</span>
          <van-radio-group v-model="form.drinking" direction="horizontal">
            <van-radio name="有">有</van-radio>
            <van-radio name="无">无</van-radio>
          </van-radio-group>
        </div>
      </div>

      <!-- 疾病史 -->
      <div class="section-title">疾病史</div>
      <div class="question-group">
        <div class="question-item" v-for="(item, index) in diseaseList" :key="index" >
          <span class="question-label">{{ item.label }}：</span>
          <van-radio-group v-model="form[item.key]" direction="horizontal">
            <van-radio name="有">有</van-radio>
            <van-radio name="无">无</van-radio>
          </van-radio-group>
        </div>
      </div>

      <!-- 其他疾病 -->
      <div class="section-title">其他</div>
      <div class="question-group">
        <van-field
          v-model="form.other"
          type="textarea"
          rows="3"
          placeholder="请填写其他情况（选填）"
        />
      </div>

      <!-- 提交按钮 -->
      <div class="submit-section">
        <van-button type="primary" block @click="submitForm">提交问卷</van-button>
      </div>
    </div>
  </div>
</template>

<script>
import { storage, ajax } from "../common";
import apiUrls from '../config/apiUrls';
import { Toast } from 'vant';

export default {
  name: 'HealthQuestionnaire',
  data() {
    return {
      form: {
        smoking: '',
        drinking: '',
        hypertension: '',
        heartDisease: '',
        cerebrovascular: '',
        diabetes: '',
        tuberculosis: '',
        gynecologic: '',
        surgery: '',
        tumor: '',
        other: ''
      },
      sex:1,
      diseaseList: [
        { label: '高血压', key: 'hypertension' },
        { label: '冠心病', key: 'heartDisease'},
        { label: '脑血管病', key: 'cerebrovascular'},
        { label: '糖尿病', key: 'diabetes' },
        { label: '肺结核', key: 'tuberculosis' },
        { label: '妇科病', key: 'gynecologic' },
        { label: '手术外伤史', key: 'surgery'},
        { label: '肿瘤', key: 'tumor' }
      ]
    };
  },
  
  created(){
    this.getQuestion();
  },
  methods: {
    getQuestion() {
      var pData = {
        openId: storage.cookie.get("openid"),
      };

      ajax.post(apiUrls.GetQuestion, pData, { nocrypt: true }).then(r => {
        var res = r.data.returnData

        console.log(JSON.parse(res.ordinaryQs))
        this.form=JSON.parse(res.ordinaryQs)

      })
    },
    submitForm() {
      // 验证表单
      const hasEmptyFields = Object.entries(this.form).some(([key, value]) => {
        if (key === 'other') return false; // 其他是选填
        return !value;
      });

      if (hasEmptyFields) {
        Toast('请完整填写问卷信息');
        return;
      }

      // 触发提交事件，将表单数据传递给父组件
      this.$emit('on-submit', this.form);
    }
  }
};
</script>

<style>
  .info-item{
  font-size: .25rem!important;
}
</style>

<style lang="scss" scoped>

.health-questionnaire {
  padding: 16px;
  background-color: #f7f8fa;
  min-height: 100vh;

}
.van-radio{
  font-size: .25rem!important;
}
.questionnaire-section {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #323233;
  margin: 16px 0 12px;
  padding-left: 8px;
  border-left: 3px solid #1989fa;
}

.question-group {
  margin-bottom: 20px;
}

.question-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #ebedf0;

  &:last-child {
    border-bottom: none;
  }
}

.question-label {
  width: 100px;
  color: #323233;
  font-size: 14px;
}

:deep(.van-radio-group) {
  flex: 1;
}

:deep(.van-radio) {
  margin-right: 20px;
}

:deep(.van-field__control) {
  font-size: 14px;
}

.submit-section {
  margin-top: 24px;
  padding: 0 16px;
}

// 移动端适配
@media screen and (max-width: 375px) {
  .health-questionnaire {
    padding: 12px;
  }

  .question-label {
    width: 80px;
    font-size: 13px;
  }

  :deep(.van-radio__label) {
    font-size: 13px;
  }
}
</style>
