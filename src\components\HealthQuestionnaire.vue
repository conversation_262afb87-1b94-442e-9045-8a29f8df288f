<template>
  <div class="health-questionnaire">
    <div class="questionnaire-section">
      <!-- 基本信息 -->
      <div class="info-table">
        <table class="basic-info">
          <tr>
            <td class="label">单位：</td>
            <td colspan="3"><van-field v-model="form.company" placeholder="请输入单位" /></td>
          </tr>
          <tr>
            <td class="label">姓名：</td>
            <td colspan="3"><van-field v-model="form.name" placeholder="请输入姓名" /></td>
          </tr>
          <tr>
            <td class="label">联系方式：</td>
            <td colspan="3"><van-field v-model="form.contact" placeholder="请输入联系方式" /></td>
          </tr>
          <tr>
            <td class="label">家属手机：</td>
            <td colspan="3"><van-field v-model="form.familyContact" placeholder="请输入家属手机" /></td>
          </tr>
        </table>
      </div>

      <!-- 一、健康状况及家族史 -->
      <div class="section-header">一、健康状况及家族史</div>

      <!-- 1. 近一年内，您觉得您的健康状况怎么样？ -->
      <div class="question-item">
        <div class="question-text">1. 近一年内，您觉得您的健康状况怎么样？</div>
        <div class="options-row">
          <van-radio-group v-model="form.healthStatus" direction="horizontal">
            <van-radio name="很好">很好</van-radio>
            <van-radio name="好">好</van-radio>
            <van-radio name="一般">一般</van-radio>
            <van-radio name="不好">不好</van-radio>
            <van-radio name="很不好">很不好</van-radio>
          </van-radio-group>
        </div>
      </div>

      <!-- 2. 本人手术史 -->
      <div class="question-item">
        <div class="question-text">2. 本人手术史：</div>
        <div class="options-row">
          <van-radio-group v-model="form.surgeryHistory" direction="horizontal">
            <van-radio name="无">无</van-radio>
            <van-radio name="有">有（手术名称）</van-radio>
          </van-radio-group>
          <van-field
            v-if="form.surgeryHistory === '有'"
            v-model="form.surgeryName"
            placeholder="请输入手术名称"
            class="conditional-input"
          />
        </div>
      </div>

      <!-- 3. 药物过敏史 -->
      <div class="question-item">
        <div class="question-text">3. 药物过敏史：</div>
        <div class="options-row">
          <van-radio-group v-model="form.allergyHistory" direction="horizontal">
            <van-radio name="无">无</van-radio>
            <van-radio name="有">有（药物名称）</van-radio>
          </van-radio-group>
          <van-field
            v-if="form.allergyHistory === '有'"
            v-model="form.allergyName"
            placeholder="请输入药物名称"
            class="conditional-input"
          />
        </div>
      </div>

      <!-- 4. 本人疾病史及家族史 -->
      <div class="question-item">
        <div class="question-text">4. 本人疾病史及家族史：</div>
        <div class="options-row">
          <van-radio-group v-model="form.diseaseHistory" direction="horizontal">
            <van-radio name="无">无</van-radio>
            <van-radio name="有">有</van-radio>
          </van-radio-group>
        </div>
      </div>

      <!-- 疾病表格 -->
      <div class="disease-table" v-if="form.diseaseHistory === '有'">
        <table>
          <thead>
            <tr>
              <th>疾病名称(译义)</th>
              <th>已患该病</th>
              <th>患病时长(年)</th>
              <th>已经用药</th>
              <th>遵医嘱按时服药</th>
              <th>家族史(父母亲及兄弟姐妹)</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(disease, index) in diseaseList" :key="index">
              <td>{{ disease.label }}</td>
              <td>
                <van-checkbox v-model="form.diseases[disease.key].hasDiagnosed" />
              </td>
              <td>
                <van-field
                  v-model="form.diseases[disease.key].duration"
                  placeholder=""
                  :disabled="!form.diseases[disease.key].hasDiagnosed"
                />
              </td>
              <td>
                <van-checkbox
                  v-model="form.diseases[disease.key].hasMedication"
                  :disabled="!form.diseases[disease.key].hasDiagnosed"
                />
              </td>
              <td>
                <van-radio-group
                  v-model="form.diseases[disease.key].followPrescription"
                  direction="horizontal"
                  :disabled="!form.diseases[disease.key].hasMedication || !form.diseases[disease.key].hasDiagnosed"
                >
                  <van-radio name="是">是</van-radio>
                  <van-radio name="否">否</van-radio>
                </van-radio-group>
              </td>
              <td>
                <van-checkbox v-model="form.diseases[disease.key].familyHistory" />
              </td>
            </tr>
            <tr>
              <td>其他疾病：</td>
              <td colspan="5">
                <van-field
                  v-model="form.otherDisease"
                  placeholder="请填写其他疾病"
                />
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 二、生活方式信息 -->
      <div class="section-header">二、生活方式信息</div>

      <!-- 4. 您吸烟吗？ -->
      <div class="question-item">
        <div class="question-text">4. 您吸烟吗？</div>
        <div class="options-row">
          <van-radio-group v-model="form.smoking" direction="horizontal">
            <van-radio name="不吸烟">不吸烟</van-radio>
            <van-radio name="吸烟">吸烟：每日平均</van-radio>
          </van-radio-group>
          <van-field
            v-if="form.smoking === '吸烟'"
            v-model="form.smokingAmount"
            placeholder="支"
            class="short-input"
          />
          <span v-if="form.smoking === '吸烟'">，已经吸烟</span>
          <van-field
            v-if="form.smoking === '吸烟'"
            v-model="form.smokingYears"
            placeholder="年"
            class="short-input"
          />
        </div>
      </div>

      <!-- 5. 您饮酒吗？ -->
      <div class="question-item">
        <div class="question-text">5. 您饮酒吗？</div>
        <div class="options-row">
          <van-radio-group v-model="form.drinking" direction="horizontal">
            <van-radio name="不饮酒">不饮酒</van-radio>
            <van-radio name="偶尔饮酒">偶尔饮酒</van-radio>
            <van-radio name="每月少于1次">每月少于1次</van-radio>
            <van-radio name="每月1~10次">每月1~10次</van-radio>
            <van-radio name="每月超过10次">每月超过10次</van-radio>
          </van-radio-group>
        </div>
      </div>

      <!-- 6. 您的饮食习惯 -->
      <div class="question-item">
        <div class="question-text">6. 您的饮食习惯：</div>
        <div class="options-row">
          <van-radio-group v-model="form.dietHabit" direction="horizontal">
            <van-radio name="荤素均衡">荤素均衡</van-radio>
            <van-radio name="荤食为主">荤食为主</van-radio>
            <van-radio name="素食为主">素食为主</van-radio>
          </van-radio-group>
        </div>
        <div class="question-text sub-question">口味偏于：</div>
        <div class="options-row">
          <van-checkbox-group v-model="form.tastePreference" direction="horizontal">
            <van-checkbox name="多盐">多盐</van-checkbox>
            <van-checkbox name="多油">多油</van-checkbox>
            <van-checkbox name="多糖">多糖</van-checkbox>
            <van-checkbox name="辛辣">辛辣</van-checkbox>
            <van-checkbox name="清淡">清淡</van-checkbox>
          </van-checkbox-group>
        </div>
      </div>

      <!-- 7. 您每天的夜间睡眠时长 -->
      <div class="question-item">
        <div class="question-text">7. 您每天的夜间睡眠时长</div>
        <div class="options-row">
          <van-field
            v-model="form.sleepHours"
            placeholder="小时"
            class="short-input"
            
          />
          <span>小时，午睡</span>
          <van-field
            v-model="form.napMinutes"
            placeholder="分钟"
            class="short-input"
          />
          <span>分钟，睡眠质量：</span>
          <van-radio-group v-model="form.sleepQuality" direction="horizontal">
            <van-radio name="较好">较好</van-radio>
            <van-radio name="一般">一般</van-radio>
            <van-radio name="较差">较差</van-radio>
          </van-radio-group>
        </div>
      </div>

      <!-- 8. 您的作息 -->
      <div class="question-item">
        <div class="question-text">8. 您的作息：</div>
        <div class="options-row">
          <van-radio-group v-model="form.workSchedule" direction="horizontal">
            <van-radio name="夜班平均4次/周及以上">夜班平均4次/周及以上</van-radio>
            <van-radio name="夜班平均2-3次/周">夜班平均2-3次/周</van-radio>
            <van-radio name="夜班平均1次/周">夜班平均1次/周</van-radio>
            <van-radio name="常白班">常白班</van-radio>
          </van-radio-group>
        </div>
      </div>

      <!-- 三、运动情况调查 -->
      <div class="section-header">三、运动情况调查</div>

      <!-- 9. 您近期运动情况 -->
      <div class="question-item">
        <div class="question-text">9. 您近期运动情况：</div>
        <div class="options-row">
          <van-radio-group v-model="form.exerciseFrequency" direction="horizontal">
            <van-radio name="很少运动">很少运动</van-radio>
            <van-radio name="1~2次/周">1~2次/周</van-radio>
            <van-radio name="3~5次/周">3~5次/周</van-radio>
            <van-radio name=">5次/周">>5次/周</van-radio>
          </van-radio-group>
        </div>
      </div>

      <!-- 10. 您近期的锻炼方式 -->
      <div class="question-item">
        <div class="question-text">10. 您近期的锻炼方式：</div>
        <div class="options-row">
          <van-checkbox-group v-model="form.exerciseTypes" direction="horizontal">
            <van-checkbox name="散步/快走">散步/快走</van-checkbox>
            <van-checkbox name="跑步">跑步</van-checkbox>
            <van-checkbox name="跳绳">跳绳</van-checkbox>
            <van-checkbox name="游泳">游泳</van-checkbox>
            <van-checkbox name="球类">球类</van-checkbox>
            <van-checkbox name="室内健身">室内健身</van-checkbox>
            <van-checkbox name="其他">其他</van-checkbox>
          </van-checkbox-group>
        </div>
      </div>

      <!-- 四、心理及精神压力 -->
      <div class="section-header">四、心理及精神压力</div>

      <!-- 11. 您认为您是一个容易焦虑或紧张的人吗？ -->
      <div class="question-item">
        <div class="question-text">11. 您认为您是一个容易焦虑或紧张的人吗？</div>
        <div class="options-row">
          <van-radio-group v-model="form.anxious" direction="horizontal">
            <van-radio name="是">是</van-radio>
            <van-radio name="否">否</van-radio>
          </van-radio-group>
        </div>
      </div>

      <!-- 12. 最近一段时间，您是否比平时更感到焦虑或担忧？ -->
      <div class="question-item">
        <div class="question-text">12. 最近一段时间，您是否比平时更感到焦虑或担忧？</div>
        <div class="options-row">
          <van-radio-group v-model="form.recentAnxiety" direction="horizontal">
            <van-radio name="是">是</van-radio>
            <van-radio name="否">否</van-radio>
          </van-radio-group>
        </div>
      </div>

      <!-- 提交按钮 -->
      <div class="submit-section">
        <van-button type="primary" block @click="submitForm">提交问卷</van-button>
      </div>
    </div>
  </div>
</template>

<script>
import { storage, ajax } from "../common";
import apiUrls from '../config/apiUrls';
import { Toast } from 'vant';

export default {
  name: 'HealthQuestionnaire',
  props: ['regno'],
  data() {
    return {
      form: {
        // 基本信息
        company: '',
        name: '',
        contact: '',
        familyContact: '',

        // 健康状况及家族史
        healthStatus: '',
        surgeryHistory: '无',
        surgeryName: '',
        allergyHistory: '无',
        allergyName: '',
        diseaseHistory: '无',
        diseases: {
          diabetes: { hasDiagnosed: false, duration: '', hasMedication: false, followPrescription: '是', familyHistory: false },
          hypertension: { hasDiagnosed: false, duration: '', hasMedication: false, followPrescription: '是', familyHistory: false },
          hyperlipidemia: { hasDiagnosed: false, duration: '', hasMedication: false, followPrescription: '是', familyHistory: false },
          heartDisease: { hasDiagnosed: false, duration: '', hasMedication: false, followPrescription: '是', familyHistory: false },
          cerebrovascular: { hasDiagnosed: false, duration: '', hasMedication: false, followPrescription: '是', familyHistory: false },
          malignantTumor: { hasDiagnosed: false, duration: '', hasMedication: false, followPrescription: '是', familyHistory: false }
        },
        otherDisease: '',

        // 生活方式信息
        smoking: '不吸烟',
        smokingAmount: '',
        smokingYears: '',
        drinking: '不饮酒',
        dietHabit: '',
        tastePreference: [],
        sleepHours: '',
        napMinutes: '',
        sleepQuality: '',
        workSchedule: '',

        // 运动情况调查
        exerciseFrequency: '',
        exerciseTypes: [],

        // 心理及精神压力
        anxious: '',
        recentAnxiety: ''
      },
      diseaseList: [
        { label: '糖尿病', key: 'diabetes' },
        { label: '高血压', key: 'hypertension' },
        { label: '血脂异常', key: 'hyperlipidemia' },
        { label: '心脏病', key: 'heartDisease' },
        { label: '脑血管病', key: 'cerebrovascular' },
        { label: '恶性肿瘤', key: 'malignantTumor' }
      ]
    };
  },

  created(){
    // 初始化表单默认值
     const userInfo=JSON.parse(storage.cookie.get('user'))
     this.getPatientinfo();
    this.initializeForm();
    // 如果需要获取已有问卷数据
    // this.getQuestion();
  },
  methods: {
    getPatientinfo(){
      var pData = {
        regno: this.regno,
      };
      ajax.post(apiUrls.GetPatientinfo, pData, { nocrypt: true }).then(r => {
        var res = JSON.parse(r.data.returnData)[0];
        this.form.name = res.name;
        this.form.contact = res.tel;
        this.form.company=res.lncName
      })
    },
    initializeForm() {
      // 为所有疾病项初始化默认值
      this.diseaseList.forEach(disease => {
        if (!this.form.diseases[disease.key]) {
          this.form.diseases[disease.key] = {
            hasDiagnosed: false,
            duration: '',
            hasMedication: false,
            followPrescription: '是',
            familyHistory: false
          };
        }
      });
    },

    getQuestion() {
      var pData = {
        openId: storage.cookie.get("openid"),
      };

      ajax.post(apiUrls.GetQuestion, pData, { nocrypt: true }).then(r => {
        var res = r.data.returnData;
        if (res && res.ordinaryQs) {
          try {
            const savedForm = JSON.parse(res.ordinaryQs);
            // 合并保存的表单数据和当前表单数据
            this.form = { ...this.form, ...savedForm };
            // 确保疾病数据结构正确
            this.initializeForm();
          } catch (e) {
            console.error('解析问卷数据失败', e);
          }
        }
      }).catch(error => {
        console.error('获取问卷数据失败', error);
      });
    },

    submitForm() {
      // 验证必填字段
      const requiredFields = [
        'company', 'name', 'contact', 'healthStatus',
        'surgeryHistory', 'allergyHistory', 'diseaseHistory',
        'smoking', 'drinking', 'dietHabit', 'sleepHours',
        'sleepQuality', 'workSchedule', 'exerciseFrequency',
        'anxious', 'recentAnxiety'
      ];

      const missingFields = requiredFields.filter(field => !this.form[field]);
  console.log(JSON.stringify(this.form))
      if (missingFields.length > 0) {
        Toast('请完整填写问卷信息');
        return;
      }

      // 如果选择了有手术史，但未填写手术名称
      if (this.form.surgeryHistory === '有' && !this.form.surgeryName) {
        Toast('请填写手术名称');
        return;
      }

      // 如果选择了有药物过敏史，但未填写药物名称
      if (this.form.allergyHistory === '有' && !this.form.allergyName) {
        Toast('请填写药物名称');
        return;
      }

      // 如果选择了吸烟，但未填写数量或年限
      if (this.form.smoking === '吸烟' && (!this.form.smokingAmount || !this.form.smokingYears)) {
        Toast('请填写吸烟数量和年限');
        return;
      }

      // 触发提交事件，将表单数据传递给父组件
      this.$emit('on-submit', this.form);
    }
  }
};
</script>

<style>
  .info-item{
    font-size: .25rem!important;
  }
</style>

<style lang="scss" scoped>
.health-questionnaire {
  padding: 16px;
  background-color: #f7f8fa;
  min-height: 100vh;
}

.van-radio, .van-checkbox {
  font-size: .25rem!important;
}

.questionnaire-section {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

/* 基本信息表格样式 */
.info-table {
  margin-bottom: 20px;
  border-bottom: 1px solid #ebedf0;
  padding-bottom: 15px;
}

.basic-info {
  width: 100%;
  border-collapse: collapse;

  tr {
    border-bottom: 1px solid #f5f5f5;
  }

  tr:last-child {
    border-bottom: none;
  }

  td {
    padding: 12px 8px;
    vertical-align: middle;
  }

  .label {
    width: 25%;
    text-align: left;
    color: #323233;
    font-size: 15px;
    white-space: nowrap;
    font-weight: 500;
    padding-left: 10px;
  }

  :deep(.van-field__control) {
    height: 36px;
    font-size: 15px;
  }
}

/* 章节标题样式 */
.section-header {
  font-size: 16px;
  font-weight: bold;
  color: #323233;
  margin: 20px 0 15px;
  padding: 8px 0;
  text-align: center;
  border-top: 1px solid #ebedf0;
  border-bottom: 1px solid #ebedf0;
  background-color: #f7f8fa;
}

/* 问题项样式 */
.question-item {
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebedf0;
}

.question-text {
  margin-bottom: 10px;
  font-size: 14px;
  color: #323233;
  font-weight: 500;
}

.sub-question {
  margin-top: 10px;
  margin-bottom: 5px;
  font-weight: normal;
}

.options-row {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  font-size: .3rem;

  .van-field {
    margin: 0 5px;
  }

  span {
    margin: 0 5px;
  }
}

/* 疾病表格样式 */
.disease-table {
  margin: 15px 0;
  overflow-x: auto;

  table {
    width: 100%;
    border-collapse: collapse;
    border: 1px solid #ebedf0;

    th, td {
      border: 1px solid #ebedf0;
      padding: 8px;
      text-align: center;
      font-size: 13px;
    }

    th {
      background-color: #f7f8fa;
      font-weight: 500;
    }

    td .van-field {
      padding: 0;
      margin: 0;
    }
  }
}

/* 输入框样式 */
.conditional-input {
  margin-left: 10px;
  width: 150px;
}

.short-input {
  width: 80% !important;
  margin: 0 5px;

  :deep(.van-field__control) {
    text-align: center;
  }
}

/* 提交按钮样式 */
.submit-section {
  margin-top: 30px;
  padding: 0 16px;
}

/* 移动端适配 */
@media screen and (max-width: 480px) {
  .health-questionnaire {
    padding: 12px;
  }

  .question-text {
    font-size: 14px;
  }

  .basic-info {
    td {
      padding: 10px 5px;
    }

    .label {
      width: 30%;
      font-size: 14px;
      padding-left: 5px;
    }

    :deep(.van-field__control) {
      height: 32px;
      font-size: 14px;
    }
  }

  :deep(.van-radio__label), :deep(.van-checkbox__label) {
    font-size: 14px;
  }

  .disease-table table th, .disease-table table td {
    padding: 5px;
    font-size: 12px;
  }

  /* 确保表单控件在移动端有足够的点击区域 */
  :deep(.van-radio), :deep(.van-checkbox) {
    margin-bottom: 8px;
    padding: 5px 0;
  }

  /* 调整选项行在移动端的布局 */
  .options-row {
    flex-direction: column;
    align-items: flex-start;

    :deep(.van-radio-group), :deep(.van-checkbox-group) {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
    }
  }
}
</style>
