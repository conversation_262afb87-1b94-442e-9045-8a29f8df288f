<template>
  <div class="signature-canvas-container">
    <canvas
      ref="signatureCanvas"
      class="signature-canvas"
      @touchstart="handleStart"
      @touchmove="handleMove"
      @touchend="handleEnd"
      @mousedown="handleStart"
      @mousemove="handleMove"
      @mouseup="handleEnd"
      @mouseleave="handleEnd"
    ></canvas>
    <div class="signature-actions">
      <van-button size="small" type="default" @click="clearSignature">清除</van-button>
      <van-button size="small" type="primary" @click="saveSignature">确认</van-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SignatureCanvas',
  props: {
    width: {
      type: Number,
      default: 300
    },
    height: {
      type: Number,
      default: 150
    },
    lineWidth: {
      type: Number,
      default: 3
    },
    lineColor: {
      type: String,
      default: '#000000'
    }
  },
  data() {
    return {
      canvas: null,
      ctx: null,
      isDrawing: false,
      lastX: 0,
      lastY: 0
    };
  },
  mounted() {
    this.initCanvas();
    window.addEventListener('resize', this.resizeCanvas);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.resizeCanvas);
  },
  methods: {
    initCanvas() {
      this.canvas = this.$refs.signatureCanvas;
      this.ctx = this.canvas.getContext('2d');

      // 设置画布大小
      this.resizeCanvas();

      // 设置线条样式
      this.ctx.lineWidth = this.lineWidth;
      this.ctx.lineCap = 'round';
      this.ctx.lineJoin = 'round';
      this.ctx.strokeStyle = this.lineColor;
    },

    resizeCanvas() {
      // 如果指定了宽度，使用指定的宽度
      let canvasWidth = this.width;

      // 如果没有指定宽度，使用容器宽度
      if (!canvasWidth) {
        canvasWidth = this.$el.clientWidth || 300;
      }

      // 设置画布大小
      this.canvas.width = canvasWidth;
      this.canvas.height = this.height;

      // 重新设置线条样式（因为重设画布大小会重置上下文）
      this.ctx.lineWidth = this.lineWidth;
      this.ctx.lineCap = 'round';
      this.ctx.lineJoin = 'round';
      this.ctx.strokeStyle = this.lineColor;
    },

    // 触摸事件处理
    handleStart(e) {
      e.preventDefault();
      this.isDrawing = true;

      const { x, y } = this.getCoordinates(e);
      this.lastX = x;
      this.lastY = y;
    },

    handleMove(e) {
      if (!this.isDrawing) return;
      e.preventDefault();

      const { x, y } = this.getCoordinates(e);
      this.draw(x, y);
    },

    handleEnd(e) {
      if (!this.isDrawing) return;
      e.preventDefault();

      this.isDrawing = false;
    },

    // 获取坐标
    getCoordinates(e) {
      let x, y;

      if (e.touches && e.touches.length > 0) {
        // 触摸事件
        const rect = this.canvas.getBoundingClientRect();
        x = e.touches[0].clientX - rect.left;
        y = e.touches[0].clientY - rect.top;
      } else {
        // 鼠标事件
        const rect = this.canvas.getBoundingClientRect();
        x = e.clientX - rect.left;
        y = e.clientY - rect.top;
      }

      return { x, y };
    },

    // 绘制
    draw(x, y) {
      this.ctx.beginPath();
      this.ctx.moveTo(this.lastX, this.lastY);
      this.ctx.lineTo(x, y);
      this.ctx.stroke();

      this.lastX = x;
      this.lastY = y;
    },

    // 清除签名
    clearSignature() {
      this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
      this.$emit('clear');
    },

    // 保存签名
    saveSignature() {
      const signatureData = this.canvas.toDataURL('image/png');
      this.$emit('save', signatureData);
    },

    // 外部调用方法：设置签名
    setSignature(signatureData) {
      if (!signatureData) {
        this.clearSignature();
        return;
      }

      const img = new Image();
      img.onload = () => {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        this.ctx.drawImage(img, 0, 0, this.canvas.width, this.canvas.height);
      };
      img.src = signatureData;
    }
  }
};
</script>

<style scoped>
.signature-canvas-container {
  width: 100%;
  margin-bottom: 10px;
}

.signature-canvas {
  width: 100%;
  border: 2px solid #dcdee0;
  border-radius: 4px;
  background-color: #fff;
  touch-action: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.signature-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 8px;
}

.signature-actions button {
  margin-left: 8px;
}

/* 移动设备横屏模式下的特殊样式 */
@media screen and (max-width: 896px) and (orientation: landscape) {
  .signature-canvas {
    border-width: 1px;
  }

  .signature-actions {
    margin-top: 5px;
  }

  .signature-actions button {
    padding: 0 10px;
    height: 28px;
    line-height: 26px;
    font-size: 12px;
  }
}
</style>
