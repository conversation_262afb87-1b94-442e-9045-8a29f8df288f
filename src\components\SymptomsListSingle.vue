<template>
  <div class="symptoms-list">
    <div class="symptoms-item" v-for="(value, key, index) in symptoms" :key="key">
      <div class="symptom-name">{{ getSymptomName(key, index + 1) }}</div>
      <div class="symptom-value">
        <van-field v-model="symptoms[key]" class="symptom-field" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SymptomsListSingle',
  props: {
    symptoms: {
      type: Object,
      required: true
    }
  },
  methods: {
    getSymptomName(key, index) {
      const symptomNames = {
        'headache': '1. 头痛',
        'dizziness': '2. 头（颈）晕',
        'vertigo': '3. 眩晕',
        'insomnia': '4. 失眠',
        'drowsiness': '5. 嗜睡',
        'dreams': '6. 多梦',
        'memoryLoss': '7. 记忆力减退',
        'irritability': '8. 易激动',
        'weakness': '9. 感觉无力',
        'lowFever': '10. 低热',
        'nightSweats': '11. 盗汗',
        'hyperhidrosis': '12. 多汗',
        'fatigue': '13. 全身倦怠',
        'libidoDecrease': '14. 性欲减退',
        'visionAbnormality': '15. 视物特殊',
        'visionDecline': '16. 视力下降',
        'eyePain': '17. 眼痛',
        'photophobia': '18. 畏明',
        'tearing': '19. 流泪',
        'visualImpairment': '20. 视觉减退',
        'dryNose': '21. 鼻干',
        'nasalCongestion': '22. 鼻塞',
        'nosebleed': '23. 流鼻血',
        'rhinorrhea': '24. 流涕',
        'tinnitus': '25. 耳鸣',
        'hearingLoss': '26. 耳聋',
        'badBreath': '27. 口臭',
        'drooling': '28. 流涎',
        'toothache': '29. 牙痛',
        'looseTeeth': '30. 牙齿松动',
        'dryThroat': '31. 咽干',
        'mouthOdor': '32. 口腔异味',
        'oralUlcer': '33. 口腔溃疡',
        'soreThroat': '34. 咽痛',
        'shortness': '35. 气短',
        'chestTightness': '36. 胸闷',
        'chestPain': '37. 胸痛',
        'cough': '38. 咳嗽',
        'sputum': '39. 咳痰',
        'hemoptysis': '40. 咯血',
        'asthma': '41. 哮喘',
        'palpitation': '42. 心悸',
        'precordialDiscomfort': '43. 心前区不适',
        'appetiteLoss': '44. 食欲减退',
        'weightLoss': '45. 消瘦',
        'nausea': '46. 恶心',
        'vomiting': '47. 呕吐',
        'abdominalDistension': '48. 腹胀',
        'abdominalPain': '49. 腹痛',
        'diarrhea': '50. 腹泻',
        'abdominalFluid': '51. 腹水',
        'constipation': '52. 便秘',
        'frequentUrination': '53. 尿频',
        'urgentUrination': '54. 尿急',
        'painfulUrination': '55. 尿痛',
        'lowerLimbPain': '56. 下肢痛',
        'skinChanges': '57. 皮肤改变',
        'rash': '58. 皮疹',
        'edema': '59. 浮肿',
        'hairLoss': '60. 脱发',
        'jointPain': '61. 关节痛',
        'limbNumbness': '62. 四肢麻木',
        'movementDifficulty': '63. 动作不灵活',
        'menstrualAbnormality': '64. 月经异常'
      };
      
      return symptomNames[key] || `${index}. 未知症状`;
    }
  }
};
</script>

<style scoped>
.symptoms-list {
  width: 100%;
  overflow-x: auto;
  margin-bottom: 15px;
  border-radius: 4px;
  background-color: #fff;
  padding: 10px;
}

.symptoms-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f2f3f5;
}

.symptom-name {
  width: 150px;
  flex-shrink: 0;
  font-size: 14px;
  color: #323233;
  padding-right: 10px;
}

.symptom-value {
  flex: 1;
}

.symptom-field {
  width: 100%;
}
</style>
