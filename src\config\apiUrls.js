/*
 * @Author: Reaper
 * @Date: 2024-12-23 14:17:34
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2025-10-28 10:51:56
 * @Description: 请填写简介
 */
export default {
    toWeChatOauth: '/OAuth/OAuth',//授权地址
    GetToken: '/api/Token/GetToken',//授权地址
    GetPersonSumList: '/api/PersonSumApi/GetPersonSumList',//获取个人号源
    GetSumTimeList: '/api/PersonSumApi/GetSumTimeList',//获取个人时段数据   
    PersonOrderAdd: '/api/OrderListApi/PersonOrderAdd',//个人订单添加
    GetTeamSumList: '/api/TeamSumApi/GetTeamSumList',//获取团体号源
    GetTeamSumTimeList: '/api/TeamSumApi/GetTeamSumTimeList',//获取团体时段数据
    TeamOrderAdd: '/api/OrderListApi/TeamOrderAdd',//团体订单添加
    FindByOrderList: '/api/OrderListApi/FindByOrderList',//获取订单数据
    FindByTjLncList: '/api/UserInfoApi/GetTjLncList',//获取所有单位数据
    userOpenid: '/api/UserInfoApi/UserInfoByOpenId',//获取openid的用户，没有就新建
    getWxConfig: '/WeChat/getWxConfig',//获取微信配置
    PersonOrderPay: '/api/OrderListApi/PersonOrderPay',//订单支付
    TeamLogin: '/api/UserInfoApi/TeamLogin',//团体登录
    TeamOrderCancel: '/api/OrderListApi/TeamOrderCancel',//团体订单撤销
    PersonOrderCancel: '/api/OrderListApi/PersonOrderCancel',//个人待支付订单撤销
    PersonOrderRefund: '/api/OrderListApi/PersonOrderRefund',//个人订单撤销（退费）
    GetClusItemList: '/api/UserInfoApi/GetClusItemList',//获取个人体检套餐
    AddClusItem: '/api/UserInfoApi/AddClusItem',//个人项目加项
    GetVehicleClusItem: '/api/UserInfoApi/GetVehicleClusItem',//获取驾驶证体检套餐
    GetTjLncListByName:'/api/UserInfoApi/GetTjLncListByName',//获取单位数据模糊查询
    GetStaffClusItem: '/api/UserInfoApi/GetStaffClusItem',//获取入职体检套餐
    GetWxRefundList: '/api/OrderListApi/GetWxRefundList',//获取微信退款记录
    GetItemCombList: '/api/UserInfoApi/GetItemCombList',//获取套餐项目
    SendVerifyCode: '/api/AliyunSmsApi/send-verify-code-cached',// 获取并发送验证码
    CheckVerifyCode: '/api/AliyunSmsApi/verify-code-cached',//验证验证码
    GetReportList: '/api/ReportApi/GetReportList',//获取报告列表
    GetReportDetail: '/api/ReportApi/GetReportDetail',//获取报告详细数据
    GetReportPDFUrl: '/api/ReportApi/GetReportPDFUrl',//获取pdf报告下载地址
    PersonOrderNoPayRefund: '/api/OrderListApi/PersonOrderNoPayRefund',//个人订单撤销（没有支付，会发送消息模板）
    SweepCodePay: '/api/SweepCodePayApi/SweepCodePay',//个人订单撤销（没有支付，会发送消息模板）
    GetInforMation: '/api/SweepCodePayApi/GetTjInforMation',//扫码付获取个人信息
    GetPayRecordList: '/api/OrderListApi/GetPayRecordList',//获取缴费记录
    GetTimeStamp: '/api/CommonApi/GetTimeStamp',//获取系统时间戳
    GetNoticeInfo: '/api/BackProtectApi/GetNoticeInfo',//获取体检须知
    SyncOrderById: '/api/OrderListApi/SyncOrderById',
    SaveGetQuestion: 'api/QuestionairesApi/SaveGetQuestion',
    GetQuestion: 'api/QuestionairesApi/GetQuestion',
    GetPacsPhotoUrl: '/api/Test/GetPacsPhotoUrl',
    GetFreePerson: '/api/CommonApi/GetFreePerson',
    UpdateFreePersonState:'/api/CommonApi/UpdateFreePersonState',
    SaveQuestions:'/api/CommonApi/SaveQuestions',
    GetPatientinfo:'api/CommonApi/GetPatientinfo',
    VerifyDirectorReviewPwd:'/api/BackProtectApi/VerifyDirectorReviewPwd',
    GetCardPerson: '/api/CommonApi/GetCardPerson',  // 获取代币管理列表
    UpdateCardPerson: '/api/CommonApi/UpdateCardPerson',  // 更新代币状态
};