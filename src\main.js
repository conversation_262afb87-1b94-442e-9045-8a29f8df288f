import Vue from 'vue'
import App from './App.vue'
import './registerServiceWorker'
import router from './router'
import store from './store'
import rem from './config/rem'
import {ajax} from './common/ajax'
import {exportHttp} from './common/exportHttp'
import { Swipe, SwipeItem } from 'vant';//轮播
import { Popup,Search,Dialog} from 'vant';//弹出层
import { Loading } from 'vant';//加载提示
import { TreeSelect } from 'vant'; //分类选择
import { Icon } from 'vant';//图标样式
import { CountDown } from 'vant';//倒计时
import {Toast} from 'vant';//轻提示框
import {Overlay,Button} from 'vant';//遮罩层加 button按钮
import { DropdownMenu, DropdownItem } from 'vant';//下拉菜单
import { Collapse, CollapseItem } from 'vant';
import { Tab, Tabs,Slider } from 'vant';
import { Calendar , Cell,Field ,Checkbox, CheckboxGroup,RadioGroup, Radio,DatetimePicker ,Picker } from 'vant';
import { ActionSheet } from 'vant';
import { PasswordInput, NumberKeyboard } from 'vant';
import axios from 'axios';
import 'vant/lib/index.css';
import './assets/font/iconfont.css'





Vue.config.productionTip = false
Vue.use(rem);
Vue.use(ActionSheet);
Vue.use(Swipe).use(SwipeItem);
Vue.use(Popup);
Vue.use(Loading);
Vue.use(Calendar);  
Vue.use(TreeSelect);
Vue.use(PasswordInput);
Vue.use(NumberKeyboard);
Vue.use(Icon);
Vue.use(CountDown);
Vue.use(Toast);
Vue.use(Overlay).use(Button);
Vue.use(DropdownMenu).use(DropdownItem);
Vue.use(Collapse).use(CollapseItem);
Vue.use(Tab);
Vue.use(Tabs);
Vue.use(Slider);
Vue.use(Search);
Vue.use(Dialog);
Vue.use(Cell);
Vue.use(Field );
Vue.use(Checkbox);
Vue.use(CheckboxGroup);
Vue.use(RadioGroup);
Vue.use(Radio);
Vue.use(DatetimePicker);
Vue.use(Picker)


//提示时长5秒
Toast.setDefaultOptions({ duration: 5000 });
//封装初始化
//动态配置json文件信息
init();

function getServerConfig (url) {
  return new Promise ((resolve, reject) => {
    axios.get(url).then((result) => {
      //console.log(result)  // 看打印出来的结果
      let config = result.data;
      Vue.prototype.baseData=config
      console.log("init ok")  // 验证是否已经把属性挂在了Vue上
      resolve();
    }).catch((error) => {
      console.log(error);
      reject()
    })
  })
}

async function init() {
  const Timestamp = new Date().getTime();
  await getServerConfig('./baseData.json?v=1'+Timestamp);
  ajax.setBaseUrl(Vue.prototype.baseData.apiHost);
  exportHttp.setBaseUrl(Vue.prototype.baseData.apiHost);
  new Vue({
    router,
    store,
    render: h => h(App)
  }).$mount('#app')
}

　Vue.directive('hideinfo',{
  
　　　bind: function(el, binding){
  // 　　　　console.log('bind:',binding.name);
  　　},
  　　inserted: function(el, binding){
          var data=el.innerText;
          data=data.replace(data.substr(data.length/3,data.length/3),"********");
          // console.log(data);
          el.innerText=data;
  // 　　　　console.log('insert:',binding.name);
  　　},
  　　update: function(el, binding, vnode, oldVnode){
  // 　　　　console.log('update:',binding.name);
  　　},
  　　componentUpdated: function(el, binding){
  // 　　　　console.log('componentUpdated:',binding.name);
  　　}
  });