import Vue from 'vue'
import VueRouter from 'vue-router'
import Index from '../views/Home/Index'
import notice from '../views/Home/notice'
import test from '../views/Home/test'
import PersonIndex from '../views/Persons/PersonIndex'
import VehicleIndex from '../views/Persons/VehicleIndex'
import HealthCertificate from '../views/Persons/HealthCertificate'

import StaffIndex from '../views/Persons/StaffIndex'
import Questionnaire from '../views/Questionnaire/Questionnaire'
import QuestionIndex from '../views/Questionnaire/QuestionIndex'
import PersonDetails from '../views/Persons/PersonDetails'
import PersonBookSum from '../views/Persons/PersonBookSum'
import PersonOrders from '../views/Persons/PersonOrders'
import PersonPayment from '../views/Persons/PersonPayment'
import TeamPayment from '../views/Team/TeamPayment'


import TeamLogin from '../views/Team/TeamLogin'
import TeamBookSum from '../views/Team/TeamBookSum'
import TeamDetails from '../views/Team/TeamDetails'
import TeamOrders from '../views/Team/TeamOrders'
import TeamAddClusItemToMe from '../views/Team/TeamAddClusItemToMe'

import TeamAddClusItem from '../views/Team/TeamAddClusItem.vue'
import MyOrderList from '../views/Orders/MyOrderList'
import OrderDetails from '../views/Orders/OrderDetails'
import PayRecord from '../views/Orders/PayRecord'
import ReportLogin from '../views/Report/ReportLogin'
// import ReportDataList from '../views/Report/ReportDataList'
// import ReportDetails from '../views/Report/ReportDetails'
import oauth from '../views/oauth'
import login from '../views/login'
import ReportList from '../views/Report/NewReport/ReportList'
import ReportMian from '../views/Report/NewReport/ReportMian'
import ReportFinal from '../views/Report/NewReport/ReportFinal'
import ReportGuide from '../views/Report/NewReport/ReportGuide'
import ReportSuggest from '../views/Report/NewReport/ReportSuggest'
import ReportDetails from '../views/Report/NewReport/ReportDetails'
import addClusItem from '../views/addClusItem'
import HospChoose from '../views/HospChoose'
import TeamComboList from '../views/Team/TeamComboList'
import HealthQuestionnaire from '../views/Questionnaire/HealthQuestionnaire' // 新增导入
import OccupationalDiseasesQs from '../views/Questionnaire/OccupationalDiseasesQs' // 职业病问卷
import MyQuestionnaire from '../views/Questionnaire/MyQuestionnaire' // 我的问卷列表
import DirectorReview from '../views/DirectorReview/DirectorReview'

import SweepCodePay from '../views/SweepCodePay/SweepCodePay'
import { storage } from '../common/index'



Vue.use(VueRouter)

const routes = [

  {
    path: '/Index',
    name: 'index',
    component: Index,
    meta:{
      Auth:false
    }
  },
  {
    path: '/',
    name: 'HospChoose',
    component: HospChoose,
    meta:{
      Auth:true
    }
  },
  {
    path: '/HealthQuestionnaire',
    name: 'HealthQuestionnaire',
    component: HealthQuestionnaire,
    meta:{
      Auth:true
    }
  },
  {
    path: '/OccupationalDiseasesQs',
    name: 'OccupationalDiseasesQs',
    component: OccupationalDiseasesQs,
    meta:{
      Auth:true
    }
  },
  {
    path: '/DirectorReview',
    name: 'DirectorReview',
    component: DirectorReview,
    meta:{
      Auth:false
    }
  },

  


  {
    path:'/test',
    name:'test',
    component:test,
    meta:{
      index:3
    }
  },
  {
    path:'/notice',
    name:'notice',
    component:notice,
    meta:{
      index:1,
      Auth:true
    }
  },
  {
    path:'/addClusItem',
    name:'addClusItem',
    component:addClusItem,
    meta:{
      index:2
    }
  },
  {
    path:'/PersonIndex',
    name:'PersonIndex',
    component:PersonIndex,
    meta:{
      index:'1',
      Auth:true
    }
  },
  {
    path:'/VehicleIndex',
    name:'VehicleIndex',
    component:VehicleIndex,
    meta:{
      index:'6',
      Auth:true
    }
  },
  {
    path:'/StaffIndex',
    name:'StaffIndex',
    component:StaffIndex,
    meta:{
      index:'7',
      Auth:true
    }
  },
  {
    path:'/HealthCertificate',
    name:'HealthCertificate',
    component:HealthCertificate,
    meta:{
      index:'10',
      Auth:true
    }
  },


  {
    path:'/Questionnaire',
    name:'Questionnaire',
    component:Questionnaire,
    meta:{
      index:'1',
      Auth:true
    }
  },
  {
    path:'/QuestionIndex',
    name:'QuestionIndex',
    component:QuestionIndex,
    meta:{
      index:'1',
      Auth:true
    }
  },
  {
    path:'/PersonDetails',
    name:'PersonDetails',
    component:PersonDetails,
    meta:{
      index:'2',
      Auth:true
    }
  },
  {
    path:'/PersonBookSum',
    name:'PersonBookSum',
    component:PersonBookSum,
    meta:{
      index:'3',
      Auth:true
    }
  },
  {
    path:'/PersonOrders',
    name:'PersonOrders',
    component:PersonOrders,
    meta:{
      index:'4',
      Auth:true
    }
  },
  {
    path:'/PersonPayment',
    name:'PersonPayment',
    component:PersonPayment,
    meta:{
      index:'5',
      Auth:true
    }
  },
  {
    path:'/MyOrderList',
    name:'MyOrderList',
    component:MyOrderList,
    meta:{
      index:'1',
      Auth:true
    }
  },
  {
    path:'/OrderDetails',
    name:'OrderDetails',
    component:OrderDetails,
    meta:{
      index:'2',
      Auth:true
    }
  },
  {
    path:'/PayRecord',
    name:'PayRecord',
    component:PayRecord,
    meta:{
      index:'1',
      Auth:true
    }
  },
  {
    path:'/TeamLogin',
    name:'TeamLogin',
    component:TeamLogin,
    meta:{
      index:'1',
      Auth:true
    }
  },
  {
    path:'/TeamAddClusItem',
    name:'TeamAddClusItem',
    component:TeamAddClusItem,
    meta:{
      index:'2',
      Auth:true
    }
  },
  {
    path:'/TeamBookSum',
    name:'TeamBookSum',
    component:TeamBookSum,
    meta:{
      index:'3',
      Auth:true
    }
  },
  {
    path:'/TeamDetails',
    name:'TeamDetails',
    component:TeamDetails,
    meta:{
      index:'4',
      Auth:true
    }
  },
  {
    path:'/TeamOrders',
    name:'TeamOrders',
    component:TeamOrders,
    meta:{
      index:'5',
      Auth:true
    }
  },

  {
    path:'/TeamComboList',
    name:'TeamComboList',
    component:TeamComboList,
    meta:{
      index:'6',
      Auth:true
    }
  },
  {
    path:'/TeamAddClusItemToMe',
    name:'TeamAddClusItemToMe',
    component:TeamAddClusItemToMe,
    meta:{
      index:'7',
      Auth:true
    }
  },

  {
    path:'/TeamPayment',
    name:'TeamPayment',
    component:TeamPayment,
    meta:{
      index:'8',
      Auth:true
    }
  },



  {
    path:'/ReportLogin',
    name:'ReportLogin',
    component:ReportLogin,
    meta:{
      index:'1',
      Auth:false
    }
  },
  // {
  //   path:'/ReportDataList',
  //   name:'ReportDataList',
  //   component:ReportDataList,
  //   meta:{
  //     index:'2'
  //   }
  // },
  // {
  //   path:'/ReportDetails',
  //   name:'ReportDetails',
  //   component:ReportDetails,
  //   meta:{
  //     index:'3',
  //     Auth:true
  //   }
  // },
  {
    path:'/oauth',
    name:'oauth',
    component:oauth,
    meta:{
      allowBack: false,
    }
  },
  // {
  //   path:'/login',
  //   name:'login',
  //   component:login,
  //   meta:{
  //     allowBack: false
  //   }
  // },
  {
    path:'/ReportList',
    name:'ReportList',
    component:ReportList,
    meta:{
      index:2,
      Auth:true
    }
  },
  {
    path:'/ReportMian',
    name:'ReportMian',
    component:ReportMian,
    meta:{
      index:3,
      Auth:true
    }
  },
  {
    path:'/ReportFinal',
    name:'ReportFinal',
    component:ReportFinal,
    meta:{
      index:4,
      Auth:true
    }
  },
  {
    path:'/ReportGuide',
    name:'ReportGuide',
    component:ReportGuide,
    meta:{
      index:5,
      Auth:true
    }
  },
  {
    path:'/ReportSuggest',
    name:'ReportSuggest',
    component:ReportSuggest,
    meta:{
      index:6,
      Auth:true
    }
  },
  {
    path:'/ReportDetails',
    name:'ReportDetails',
    component:ReportDetails,
    meta:{
      index:6,
      Auth:true
    }
  },
  {
    path:'/SweepCodePay',
    name:'SweepCodePay',
    component:SweepCodePay,
    meta:{
      index:1,
      Auth:true
    }
  },
  {
    path:'/MyQuestionnaire',
    name:'MyQuestionnaire',
    component:MyQuestionnaire,
    meta:{
      index:7,
      Auth:false
    }
  }

]

const router = new VueRouter({
  mode: 'hash',
  routes

})

router.beforeEach((to,from,next)=>{
  // 强制给index.html 加上版本
  if (document.URL.indexOf('?v='+Vue.prototype.baseData.version) < 0 && (Vue.prototype.baseData.version||'')!='') {

    window.location.href = '?v=' + Vue.prototype.baseData.version + '#' + to.fullPath
    // console.log(pwindow.location.athname+'?v=' + Vue.prototype.baseData.version + '#' + to.fullPath);
    return;
  }

  if (to.meta.Auth) {
    if (storage.cookie.get('user') != null) {
      next();
    } else {
     storage.session.set("redirect", to.fullPath);
      next({
        path: '/oauth',
        query: {
          type:"jump"
        }
      })
    }
  } else {
    if(storage.cookie.get('isOauth')==1)
    {
      storage.cookie.set('isOauth','0');
      next({ path:storage.session.get("redirect") });
    }
    next();
  }
})

export default router
