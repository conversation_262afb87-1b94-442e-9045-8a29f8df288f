import Vue from 'vue'
import { storage,ajax } from '../common';
let baseData = Vue.prototype.baseData;
import qs from 'qs'
 
 
  function getToken() {
    try 
    {
      var toekntime=  new Date(storage.session.get("tokenTime"))
      if(toekntime<new Date())
      {
        return null;
      }
      var token=storage.session.get('token');
      if(token==null||token=='')
      {
          return null;
      }
    return  JSON.parse(token);
    }
    catch(err){
      console.log(err);
      return null;
    }
  }
  
    function setToken(token) {
     
      if(token==null||token==undefined||token.expires_in==null)
      {
        return false;        
      }
      storage.session.set('tokenTime', new Date(new Date().getTime()+(token.expires_in*1000)-10000));
      storage.session.set('token', JSON.stringify(token));
      return true;
  }
  
    function removeToken() {
 
    return storage.session.remove(token)
  }

  function toWeChatOauth(type)
  {
 
      let wxurl= baseData.authorize_url+'appid='+baseData.appid+'&redirect_uri='+
      baseData.redirect_uri+'&response_type=code&scope=snsapi_base&state='+type+'&connect_redirect=1#wechat_redirect';
      return wxurl;
 
  }
  

  
  function apiGetToken()
  {
        //获取token授权登录
           var data = {
             //认证类型 
             grant_type: 'password',
             client_id: 'clientanduser',
             client_secret:'secret',
             username: "m1",
             password: "password",
             scope:"api1"
           }
           //注意这里需要使用fromdata的请求
         ajax.post("/connect/token", qs.stringify(data),{headers:{ 'content-type':'application/x-www-form-urlencoded'},isLoginstate:true}).then(r=>{
           r=r.data;
           console.log(r);
           setToken(r); 
         }).catch(e=>{
           console.log(e);
         });
  }

  function isLoginstate()
  {
    return !(storage.session.get('user')==null||storage.session.get('user')==undefined||storage.session.get('user')=='');
  }

export const authService = {
    setToken,
    removeToken,
    getToken,
    toWeChatOauth,
    apiGetToken,
    isLoginstate
};
