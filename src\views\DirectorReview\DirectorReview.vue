<template>
  <div class="director-review">
    <div class="header">
      <div class="back-button" @click="goBack">
        <van-icon name="arrow-left" size="20" />
      </div>
      <div class="title">院长审核</div>
    </div>

    <div class="content">
      <div v-if="loading" class="loading-container">
        <van-loading type="spinner" color="#1989fa" />
        <div class="loading-text">加载中...</div>
      </div>

      <div v-else-if="reviewList.length === 0" class="empty-container">
        <div class="empty-icon">
          <van-icon name="info-o" size="48" />
        </div>
        <div class="empty-text">暂无待审核项目</div>
      </div>

      <div v-else class="review-list">
        <div v-for="(item, index) in reviewList" :key="index" class="review-item" @click="viewReviewDetail(item)">
          <div class="review-item-header">
            <div class="review-item-title">体检号：{{ item.regNo }}</div>
            <div class="review-item-status" :class="getStatusClass(item.isPay)">{{ getStatusText(item.isPay) }}</div>
          </div>
          <div class="review-item-content">
            <div class="review-item-info">
              <div class="info-row">
                <span class="label">申请人：</span>
                <span class="value">{{ item.patName }}</span>
              </div>
              <!-- <div class="info-row">
                <span class="label">申请时间：</span>
                <span class="value">{{ formatDate(item.applyTime) }}</span>
              </div> -->
              <div class="info-row">
                <span class="label">证件号码：</span>
                <span class="value">{{ item.idcard }}</span>
              </div>
            </div>
          </div>
          <div class="review-item-footer">
            <van-button size="small" type="primary" @click.stop="approveItem(item)">审核通过</van-button>
            <!-- <van-button size="small" type="danger" @click.stop="rejectItem(item)">驳回</van-button> -->
          </div>
        </div>
      </div>
    </div>
    <van-action-sheet v-model="DirectorReviewShow" :close-on-click-overlay="false" :closeable="false" title="登录">

<div class="DirectorReviewLogin" style="background: #fff;">
  <!-- 密码输入框 -->
 
  <van-password-input
  :value="DirectorReviewValue"
  :mask="false"
  :focused="showKeyboard"
  @focus="showKeyboard = true"
/>
  <!-- 数字键盘 -->
  <van-number-keyboard v-model="DirectorReviewValue" :show="showKeyboard"  />
</div>
</van-action-sheet>
  </div>
</template>

<script>
import { Toast } from "vant";
import { ajax, storage } from "../../common";
import apiUrls from "../../config/apiUrls";

export default {
  name: "DirectorReview",
  data() {
    return {
      pws: "",
      DirectorReviewValue: '',
      DirectorReviewShow: true,
      showKeyboard:true,
      loading: true,
      reviewList: [

      ]
    };
  },
  watch: {
   
    DirectorReviewValue(value) {
      if (value.length === 6) {
        if (this.verifyDirectorReviewPwd(value)) {
          Toast("登陆成功！")
          this.DirectorReviewShow = false;
          this.getReviewList();
        } else {
          this.errorInfo = '密码错误';
          Toast("密码错误")
        }
      } else {
        this.errorInfo = '';
      }
    }
  },
  created() {
    // 模拟API加载
    setTimeout(() => {
      this.loading = false;
    }, 1000);

    // 实际项目中应该调用API获取数据
    this.getReviewList();
  },
  methods: {
    verifyDirectorReviewPwd(DirectorReviewValue){
      var pData = {
        id:1,
        pwd: DirectorReviewValue
      }
       ajax
        .post(apiUrls.VerifyDirectorReviewPwd, pData)
        .then(response => {
          return response.data.returnData
        })
        .catch(error => {
          Toast("获取审核列表失败，请稍后重试");
          this.loading = false;
        });
    },
    goBack() {
      this.$router.go(-1);
    },
    getReviewList() {
      this.loading = true;
      // 实际项目中应该调用API获取数据
      ajax
        .post(apiUrls.GetFreePerson, {})
        .then(response => {
          this.reviewList = JSON.parse(response.data.returnData);
          this.loading = false;
        })
        .catch(error => {
          Toast("获取审核列表失败，请稍后重试");
          this.loading = false;
        });
    },
    formatDate(dateString) {
      if (!dateString) return "";
      return dateString;
    },
    getStatusText(status) {
      const statusMap = {
        F: "待审核",
        T: "已通过",
        rejected: "已驳回"
      };
      return statusMap[status] || "未知状态";
    },
    getStatusClass(status) {
      return `status-${status}`;
    },
    viewReviewDetail(item) {
      // 查看详情逻辑
      Toast(`查看详情: ${item.title}`);
    },
    approveItem(item) {
      var pData = {
        regno: item.regNo,
        kw: 'T'
      }
      ajax
        .post(apiUrls.UpdateFreePersonState, pData, { nocrypt: true }).then(r => {

          this.getReviewList()
        })

      // 审核通过逻辑
      Toast(`审核通过`);
    },
    rejectItem(item) {
      // 驳回逻辑
      Toast(`驳回: ${item.title}`);
    }
  }
};
</script>

<style lang="scss" scoped>
.DirectorReviewLogin{
  height: 100vh;
}
.director-review {
  width: 100%;
  min-height: 100vh;
  background-color: #f7f8fa;
}

.header {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 46px;
  background-color: #fff;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.back-button {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
}

.title {
  font-size: 18px;
  font-weight: 500;
  color: #323233;
}

.content {
  padding: 16px;
}

.loading-container,
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
}

.loading-text,
.empty-text {
  margin-top: 12px;
  font-size: 14px;
  color: #969799;
}

.review-list {
  margin-bottom: 20px;
}

.review-item {
  margin-bottom: 12px;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.review-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f2f3f5;
}

.review-item-title {
  font-size: 16px;
  font-weight: 500;
  color: #323233;
}

.review-item-status {
  font-size: 14px;
  padding: 2px 8px;
  border-radius: 4px;
}

.status-pending {
  color: #ff976a;
  background-color: #fff7e8;
}

.status-approved {
  color: #07c160;
  background-color: #e8f7ef;
}

.status-rejected {
  color: #ee0a24;
  background-color: #fef0f0;
}

.review-item-content {
  padding: 12px 16px;
}

.review-item-info {
  font-size: 14px;
}

.info-row {
  display: flex;
  margin-bottom: 8px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.label {
  color: #969799;
  width: 80px;
}

.value {
  color: #323233;
  flex: 1;
}

.review-item-footer {
  display: flex;
  justify-content: flex-end;
  padding: 12px 16px;
  border-top: 1px solid #f2f3f5;
}

.review-item-footer .van-button {
  margin-left: 8px;
}
</style>
