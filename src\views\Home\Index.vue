<template>
  <div>
    <!-- 轮播图 -->
    <!-- <van-swipe :autoplay="3000" indicator-color="white" > -->
    <div class="topImg"><van-swipe :autoplay="5000" indicator-color="white">
        <van-swipe-item><img src="../../assets/icon/swipetwo.png" alt=""></van-swipe-item>
        <van-swipe-item><img src="../../assets/banner2.png" alt=""></van-swipe-item>
        <van-swipe-item><img src="../../assets/banner3.png" alt=""></van-swipe-item>
      </van-swipe></div>
    <!-- 体检类别 -->
    <div class="test">
      <!-- 类别按钮 -->
      <div class="category" style="height:3.2rem;background: #efefefe8;">
        <div class="categoryTitle" style="height:1rem;">
          <!-- <div class="blackBox"></div> -->
          <!-- <span>体检类别</span> -->
        </div>
        <div class="categoryBtn">
          <div class="SmallBox" @click="toPersonal">
            <div class="SmallText">
              <span>个人体检</span>
              <span>用于个人健康体检</span>
            </div>
            <div class="SmallImg">
              <img src="../../assets/icon/nurse.png" alt="个人体检" />
            </div>
          </div>
          <div class="SmallBoxL" @click="toTeamTest">
            <div class="SmallText">
              <span>团体体检</span>
              <span>用于公司团队体检</span>
            </div>
            <div class="SmallImg">
              <img src="../../assets/icon/doctor.png" alt="团队体检" />
            </div>
          </div>
        </div>
      </div>


      <!-- 个人中心 -->

      <!-- 类别按钮 -->
      <div class="category">
        <div class="categoryTitle">
          <div class="blackBox"></div>
          <span>个人中心</span>
        </div>
        <div class="BtnIconGrid">
          <div class="grid-item" @click="toMyAppointment">
            <img src="../../assets/icon/iconOrder.png" alt="我的预约" />
            <span>我的预约</span>
          </div>
          <div class="grid-item" @click="MyReport">
            <img src="../../assets/icon/iconReport.png" alt="我的报告" />
            <span>我的报告</span>
          </div>
          <div class="grid-item" @click="toMyQuestionnaire" v-if="false">
            <img src="../../assets/PayRecord.png" alt="我的问卷" />
            <span>我的问卷</span>
          </div>
          <div class="grid-item" @click="$router.push('/DirectorReview')">
            <img src="../../assets/PayRecord.png" alt="缴费记录" />
            <span>院长审核</span>
          </div>
        </div>

        <!-- <div class="BtnIcon">
          <div @click="toPayRecord">
            <img src="../../assets/PayRecord.png" alt="缴费记录" />
            <span>缴费记录</span>
          </div>
          <div></div>
        </div> -->
      </div>
    </div>

    <!-- 底部 -->
    <div class="LogoFoot">
      <img src="../../assets/HomeLogo.png" alt="医院Logo" class="LogoImg" />
      <!-- <span class="LogoTitle">{{ baseData.hospitalName }}</span>
      <span class="LogoText">{{ baseData.hospitalTitle }}</span> -->
      <div class="address">
        <img src="../../assets/address.png" alt="地址" />
        <span>{{ hospInfo.hospAddr }}</span>
      </div>
      <div class="phone">
        <img src="../../assets/phone.png" alt="联系方式" />
        <span><a :href="'tel:'+hospInfo.hospTel">{{hospInfo.hospTel}}</a></span>
      </div>
    </div>
    <!-- 体检须知 -->
    <div class="Notice" @click="Notice">
      <div class="Notice1">
        <img src="../../assets/icon/iconNotice.png" alt="">
        <div class="NoticeSpan">
          <span>体检</span>
          <span>须知</span>
        </div>
      </div>
      <div class="Notice2">
        <span>1.提前预约，为了能成功提交订单，您最晚要在体检前1天（具体以网站公示的号源情况为准）预定，请尽早预订。
        </span><br>
        <div>2.如入职单位对体检项目有特殊要求入职单位对体检项，请于入职单位对体检项</div>
      </div>
    </div>
    <!--弹窗选择-->
    <van-popup v-model="show">

      <div>
        <div style="width:80%; margin:.2rem auto ; height:20px;">
          <span style="color: red;">*</span> 婚姻状况调查：
        </div>
        <div style="padding-left: 10%;">
          <van-radio-group v-model="radio" class="hunyunR">
            <van-radio name="2" style="padding: 0.5rem;">已婚</van-radio>
            <van-radio name="1">未婚</van-radio>
            <!-- <van-radio name="3">离异</van-radio>
            <van-radio name="4">丧偶</van-radio> -->
          </van-radio-group>
        </div>
      </div>

      <div style="width:80%; margin:0 auto 12px; height:20px;"><span style="color: red;">*</span>根据需要，请选择：</div>
      <div class="optional" @click="PersonCommon('person')">
        <div class="popcom">
          <img src="../../assets/icon/iconoptional.png" alt="个人健康体检" />
          <span>个人健康体检</span>
        </div>
        <div class="nextpage">
          <img src="../../assets/icon/iconnextpage.png" alt="指引" />
        </div>
      </div>
      <!-- <div class="question" @click="PersonCommon('question')">
        <div class="popcom">
          <img src="../../assets/icon/iconquestion.png" alt="调查问卷" />
          <span>问卷推荐</span>
        </div>
        <div class="nextpage">
          <img src="../../assets/icon/iconnextpage.png" alt="指引" />
        </div>
      </div> -->




<div class="pecommon" @click="PersonCommon('healthCertificate')">
        <div class="popcom">
          <img src="../../assets/icon/iconvehicle.png" alt="健康证体检" />
          <span>健康证体检</span>
        </div>
        <div class="nextpage">
          <img src="../../assets/icon/iconnextpage.png" alt="指引" />
        </div>
      </div>

      <div class="pecommon" @click="PersonCommon('vehicle')">
        <div class="popcom">
          <img src="../../assets/icon/iconvehicle.png" alt="驾驶证体检" />
          <span>驾驶证体检</span>
        </div>
        <div class="nextpage">
          <img src="../../assets/icon/iconnextpage.png" alt="指引" />
        </div>
      </div>
      <div class="pestaff" @click="PersonCommon('staff')">
        <div class="popcom">
          <img src="../../assets/icon/iconstaff.png" alt="入职体检" />
          <span>入职体检</span>
        </div>
        <div class="nextpage">
          <img src="../../assets/icon/iconnextpage.png" alt="指引" />
        </div>
      </div>
    </van-popup>

    <!--弹窗选择-->
    <!-- <div style="background-color: aqua;">
      <van-dialog v-model="showHun" title="婚姻状况调查" show-cancel-button :beforeClose="beforeClose">
        <div style="width:80%;margin:0 auto;">
          <van-radio-group v-model="radio" class="hunyunR">
            <van-radio name="2" style="padding: 0.5rem;">已婚</van-radio>
            <van-radio name="1">未婚</van-radio>
            <van-radio name="3">离异</van-radio>
            <van-radio name="4">丧偶</van-radio>
          </van-radio-group>
        </div>
      </van-dialog>
    </div> -->

    <van-dialog v-model="show_wj" show-cancel-button cancelButtonText="否" confirmButtonText="是"
      :beforeClose="beforeClose_wj">
      <div style="margin: 0.5rem .5rem;text-indent:2em ;">若您需要评估自身健康情况，可前往健康问卷自测，是否前往？</div>
    </van-dialog>

    <!-- <van-dialog v-model="showTip" title="温馨提示" @confirm="isCancleTip()">
      <div class="tipdialog_container" v-html="defaulDialogText"></div>
    </van-dialog> -->
    
  </div>
</template>
<script>
import { Toast } from "vant";
import Vue from "vue";
import { ajax, storage } from "../../common";
import apiUrls from "../../config/apiUrls";
export default {
  data() {
    return {
      
      show: false,
      showTip: false,
      showHun: false,
      show_wj: false,
      radio: "",
      defaulDialogText: Vue.prototype.baseData.defaulDialogText,
      publicPath: process.env.BASE_URL,
      baseData: Vue.prototype.baseData,
      type: "",
      hospInfo:{}
    };
  },
  created() {
    // storage.session.delete("typeHunyun");
   this.hospInfo = JSON.parse(storage.session.get("hospInfo"));
    storage.session.delete("questionInfoZC");
  },
  methods: {
    //弹出显示
    toPersonal() {
      this.type = "";
      storage.session.delete("dataList");
      storage.session.delete("chooseItemPerson");
      storage.session.delete("clusIncludeItem");
      if(storage.session.get("typeHunyun")){
        this.radio=storage.session.get("typeHunyun")
      }
      this.show = true;
    },
    PersonCommon(type) {
      if (this.radio) {
        storage.session.set("typeHunyun", this.radio);
        storage.session.set("type", type);
        this.type = type;
        this.showTip = false;
        this.show_wj = true;
      } else {
        Toast("请完成婚姻状况调查！");
        return;
      }
    },
    PersonCommonB(type) {
      // if (this.radio) {
      //   storage.session.set("typeHunyun", this.radio);
      // } else {
      //   Toast("请完成上面选择题！");
      //   return;
      // }
      this.showTip = false;
      storage.session.set("type", type);
      // let type = storage.session.get("type");
      switch (type) {
        case "person":
          this.$router.push({
            path: "/PersonIndex",
          });
          break;
        case "vehicle":
          this.$router.push({
            path: "/VehicleIndex",
          });
          break;
        case "staff":
          this.$router.push({
            path: "/StaffIndex",
          });
          break;
          case "healthCertificate":
          this.$router.push({
            path: "/HealthCertificate",
          });
          break;


        case "question":
          storage.session.set("type", "person");
          // window.location.href="http://hlhjkpg.krmanager.com"
          break;
      }
    },
    // beforeClose(action, done) {
    //   if (action === 'confirm') {
    //     if (this.radio) {
    //       storage.session.set("typeHunyun", this.radio);
    //       this.PersonCommonB(this.type)
    //       this.showHun = false;
    //       done()
    //       return;
    //     }
    //     done(false)
    //     Toast("请完成选择题！");
    //     return;
    //   }
    //   done();
    // },
    beforeClose_wj(action, done) {
      if (action === 'confirm') {
        this.$router.push({
          path: "/HealthQuestionnaire",
          query: {
            page: this.type
          }
        });
        done()
        return;
      }
      this.PersonCommonB(this.type)
      this.show_wj = false;
      done();
    },
    //个人体检选择
    // PersonCommon(type) {
    //   this.showTip = true;
    //   storage.session.set("type", type);
    // },
    //个人体检选择
    // PersonCommon(type) {
    //   switch (type) {
    //     case "person":
    //       storage.session.set("type", "person");
    //       this.$router.push({
    //         path: "/PersonIndex"
    //       });
    //       break;
    //     case "question":
    //       storage.session.set("type", "person");
    //       window.location.href="http://hlhjkpg.krmanager.com"
    //       break;
    //     case "vehicle":
    //       storage.session.set("type", "vehicle");
    //       this.$router.push({
    //         path: "/VehicleIndex"
    //       });
    //       break;
    //     case "staff":
    //       storage.session.set("type", "staff");
    //       this.$router.push({
    //         path: "/StaffIndex"
    //       });
    //       break;
    //   }
    // },
    //团体体检
    toTeamTest() {
      this.$router.push({
        path: "/TeamLogin"
      });
    },
    //我的预约
    toMyAppointment() {
      this.$router.push({
        path: "/MyOrderList"
      });
    },
    //我的报告
    MyReport() {
      this.$router.push({
        path: "/ReportLogin"
      });
    },
    //我的问卷
    toMyQuestionnaire() {
      this.$router.push({
        path: "/MyQuestionnaire"
      });
    },
    toPayRecord() {
      this.$router.push({
        path: "/PayRecord"
      });
    },
    //体检须知
    Notice() {
      this.$router.push({
        path: "/notice"
      });
    },
    //微信支付测试
    // ssss() {
    //   var pData = { code: "" };
    //   ajax
    //     .post(apiUrls.PersonOrderPay, pData, { nocrypt: true })
    //     .then(r => {
    //       var data = r.data.returnData;
    //       alert(JSON.stringify(data));
    //       WeixinJSBridge.invoke(
    //         "getBrandWCPayRequest",
    //         {
    //           appId: data.appid, //公众号名称，由商户传入
    //           timeStamp: data.timeStamp, //时间戳
    //           nonceStr: data.nonceStr, //随机串
    //           package: data.package, //扩展包
    //           signType: "MD5", //微信签名方式:MD5
    //           paySign: data.paySign //微信签名
    //         },
    //         function(res) {
    //           alert(JSON.stringify(res));

    //           if (res.err_msg == "get_brand_wcpay_request:ok") {
    //             if (confirm("支付成功！点击“确定”进入退款流程测试。")) {
    //             }
    //             //console.log(JSON.stringify(res));
    //           }
    //           // 使用以上方式判断前端返回,微信团队郑重提示：res.err_msg将在用户支付成功后返回ok，但并不保证它绝对可靠。
    //           //因此微信团队建议，当收到ok返回时，向商户后台询问是否收到交易成功的通知，若收到通知，前端展示交易成功的界面；若此时未收到通知，商户后台主动调用查询订单接口，查询订单的当前状态，并反馈给前端展示相应的界面。
    //         }
    //       );
    //     })
    //     .catch(e => {
    //       console.log(e);
    //     });
    // }
  }
};
</script>
<style lang="scss" scoped>

.DirectorReviewLogin {
    width: 100vw;
    height: 100%;
    z-index: 10000;
  }

.van-swipe {
  height: 4rem !important;
}

.topImg {
  width: 100%;
  height: 4rem;

  img {
    width: 100%;
    height: 100%;
  }
}

/* 体检类别部分 */
.test {
  width: 100%;
  min-height: 6.2rem;
  box-sizing: border-box;
  font-size: 0.32rem;
  color: #4a4a4a;
  letter-spacing: -0.02px;
  background: white;
}

.test .category {
  width: 100%;
  min-height: 3rem;
}

.category .categoryTitle {
  width: 100%;
  height: 0.64rem;
  display: flex;
  align-items: center;
}

.categoryTitle .blackBox {
  width: 0.08rem;
  height: 0.24rem;
  background: #31BFB4;
  margin-left: 0.28rem;
}

.categoryTitle span {
  margin-left: 0.12rem;
  font-size: .32rem;
  color: #4A4A4A;
  letter-spacing: -0.01px;
  font-weight: 600;
}

.category .categoryBtn {
  width: 100%;
  height: 2rem;
  display: flex;
}

.categoryBtn .SmallBox {
  width: calc((100% - 0.62rem) / 2);
  height: 2rem;
  display: flex;
  background: #daebfc;
  border-radius: 4px;
  margin-left: 0.24rem;
  align-items: flex-end;
}

.categoryBtn .SmallBoxL {
  width: calc((100% - 0.62rem) / 2);
  margin-left: 0.14rem;
  height: 2rem;
  display: flex;
  background: #daebfc;
  border-radius: 4px;
  align-items: flex-end;
}

.categoryBtn .SmallText {
  width: 60%;
  height: 1.84rem;
  display: flex;
  flex-direction: column;
}

.SmallText span:nth-child(1) {
  letter-spacing: -0.02px;
  margin-left: 0.14rem;
  font-weight: 500;
}

.SmallText span:nth-child(2) {
  font-size: .18rem;
  color: #9b9b9b;
  letter-spacing: -0.01px;
  margin-left: 0.14rem;
  margin-top: 0.12rem;
}

.categoryBtn .SmallImg {
  width: 40%;
  height: 1.84rem;
  display: flex;
  justify-content: center;
  align-items: center;
}

.SmallImg img {
  width: 1rem;
  height: 1.8rem;
}

.Notice {
  width: 94%;
  height: 1.6rem;
  background: white;
  box-shadow: 0 2px 4px 0 rgba(153, 153, 153, 0.50);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 3.2rem;
  left: 3%;

  .Notice1 {
    width: 1.2rem;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    img {
      width: 32px;
      height: 32px;
    }

    .NoticeSpan {
      width: 100%;
      height: .4rem;
      font-size: .28rem;
      font-weight: 600;
      margin-top: 3px;

      span:nth-child(1) {
        color: #4A90E2;
      }

      span:nth-child(2) {
        color: #e2d84a;
      }
    }
  }

  .Notice2 {
    width: 5rem;
    height: 1.28rem;
    font-size: .24rem;
    margin-left: 10px;
    color: #666666;

    div {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

// .Notice div:nth-child(1) {
//   width: 50%;
//   height: 0.84rem;
//   display: flex;
//   align-items: center;
// }

// .Notice div:nth-child(1) img {
//   margin-left: 0.24rem;
// }

// .Notice div:nth-child(1) span {
//   margin-left: 0.12rem;
//   font-size: 0.28rem;
//   letter-spacing: -0.01px;
// }

// .Notice div:nth-child(2) {
//   width: 50%;
//   height: 0.84rem;
//   display: flex;
//   align-items: center;
//   justify-content: flex-end;
//   margin-right: 0.24rem;
// }

// .Notice div:nth-child(2) img {
//   width: 0.16rem;
//   height: 0.32rem;
// }

/* 个人中心 */
.BtnIcon {
  width: 100%;
  height: 2.34rem;
  font-size: 0.32rem;
  letter-spacing: -0.02px;
  display: flex;
  flex-wrap: wrap;
  border-top: 0.02rem solid #EBE8E8;
}

.BtnIcon div:nth-child(1),
.BtnIcon div:nth-child(3),
.BtnIcon div:nth-child(5) {
  width: calc((100% - 2px) / 3);
  height: 2.34rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.BtnIcon div:nth-child(2),
.BtnIcon div:nth-child(4) {
  width: 1px;
  height: 1.8rem;
  background: #EBE8E8;
}

.BtnIcon div img {
  width: 1.3rem;
  height: 1.3rem;
}

.BtnIcon div span {
  margin-top: 0.2rem;
}

/* 两行两列网格布局 */
.BtnIconGrid {
  width: 100%;
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 0.2rem;
  padding: 0.3rem;
  box-sizing: border-box;
}

.grid-item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 0.3rem 0;
  background-color: #f8f8f8;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.grid-item img {
  width: 0.8rem;
  height: 0.8rem;
  margin-bottom: 0.2rem;
}

.grid-item span {
  font-size: 0.28rem;
  color: #4a4a4a;
  letter-spacing: -0.01px;
}

/* 底部 */
.LogoFoot {
  width: 100%;
  height: 5.14rem;
  background: white;
  margin-top: 10px;
  font-size: 0.32rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  letter-spacing: -0.02px;
  /* margin-bottom: .36rem; */
}

.LogoImg {
  width: 90%;
}

.LogoFoot .LogoTitle {
  color: #4a4a4a;
  margin-top: 0.246rem;
}

.LogoFoot .LogoText {
  font-size: 0.28rem;
  margin-top: 0.16rem;
  color: #6a9be4;
  letter-spacing: -0.01px;
}

.address {
  font-size: 0.28rem;
  margin-top: 0.16rem;
  color: #9b9b9b;
  letter-spacing: -0.01px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.phone {
  width: 6.78rem;
  height: 0.96rem;
  border: 0.02rem solid #6a9be4;
  border-radius: 0.1rem;
  margin-top: 0.36rem;
  display: flex;
  justify-content: center;
  align-items: center;
}

.phone span {
  margin-left: 0.16rem;
  color: #6a9be4;
  letter-spacing: -0.02px;
}

//弹窗样式
.van-popup {

  background: #F5F5F5 !important;
  border-radius: 8px;
  height: auto;
  min-height: 6rem !important;
}

.van-overlay {
  background-color: rgba(73, 73, 73, 0.7) !important;

}

.pecommon {
  height: 0.9rem;
  width: 80%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 12px auto 0 !important;
  background: #FFFFFF;
  box-shadow: 0 2px 4px 0 rgba(153, 153, 153, 0.50);
  border-radius: 4px;
}

.pestaff {
  height: 0.9rem;
  width: 80%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 12px auto 0 !important;
  background: #FFFFFF;
  box-shadow: 0 2px 4px 0 rgba(153, 153, 153, 0.50);
  border-radius: 4px;
}

.pecommonImg {
  width: 65%;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.pecommonImg img {
  width: 25px;
  height: 25px;
}

.pecommonImg span {
  margin-left: 8%;
}

.nextpage {
  width: 30%;
  display: flex;
  justify-content: flex-end;
  align-items: center;

  img {
    width: 18px;
    height: 18px;
  }
}

.optional {
  height: 0.9rem;
  width: 80%;
  margin: 0 auto;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #FFFFFF;
  box-shadow: 0 2px 4px 0 rgba(153, 153, 153, 0.50);
  border-radius: 4px;
}

.question {
  height: 0.9rem;
  width: 80%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 12px auto 0 !important;
  background: #FFFFFF;
  box-shadow: 0 2px 4px 0 rgba(153, 153, 153, 0.50);
  border-radius: 4px;
}

.popcom {
  width: 65%;
  height: 30px;
  display: flex;
  //   justify-content: center;
  align-items: center;
  font-size: .28rem;
}

.popcom img {
  width: 0.34rem;
  height: 0.34rem;
}

.popcom span {
  margin-left: 8%;
}

.van-popup--center {
  top: 50%;
  left: 50%;
  -webkit-transform: translate3d(-50%, -50%, 0);
  transform: translate3d(-50%, -50%, 0);
  width: 80%;
  height: 50%;
  border-radius: 16px;
  z-index: 2002;
  display: flex;
  flex-direction: column;
  justify-content: center;
  font-size: .28rem;
  color: #4a4a4a;
}

.tipdialog_container {
  width: 90%;
  margin: 0 auto;
  max-height: 60vh;
  overflow-y: auto;
}

.tipdialog_container::-webkit-scrollbar {
  width: 0;
}

.tipdialog_container .dialog1_content .van-checkbox {
  align-items: start !important;
}

::v-deep .van-dialog__header {
  // font-size: 0.4rem;
  font-weight: 800;
}

.hunyunR {
  display: flex;
  align-items: center;
  flex-direction: row;
  // margin: 0.4rem;
}
</style>
