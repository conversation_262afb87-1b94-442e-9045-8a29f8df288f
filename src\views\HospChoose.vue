<template>
  <div class="hosp-choose">
    <div class="title">请选择院区</div>
    <van-cell-group>
      <!-- <van-cell 
        v-for="(item,index) in hospList" 
        :key="index" class="cell" 
        :title="item.hospName" 
        icon="location-o" 
        is-link @
        click="toHospital(item.hospCode)" 
      /> -->
      <van-cell
        v-for="(item, index) in hospList"
        :key="index"
        center
        class="cell"
        :title="item.hospName"
        is-link
        @click="toHospital(item)"
      >
        <template #title>
          <div class="hosp_item">
            <div><img :src="item.hospLogo" /></div>
            <!-- <div><img :src="require('@/assets/HomeLogo.jpg')" /></div> -->
            <div>
              <div style="font-weight: bold;">{{ item.hospName }}</div>
              <div style="font-size: .25rem;">{{ item.hospAddr }}</div>
            </div>
          </div>
        </template>
      </van-cell>
    </van-cell-group>
  </div>
</template>

<script>
import { ajax, storage, toolsUtils } from "../common";
import apiUrls from "../config/apiUrls";
import { Toast } from "vant";
import Vue from "vue";

export default {
  data() {
    return {
      hospList: [
        {
          hospName: "交通路店",
          hospCode: "01",
          hospTel: "0371-56533900",
          hospAddr: "郑州蓝天健康体检服务院二七蓝天门诊部",
          hospLogo: require("../assets/hosp01.png"),
        },
        {
          hospName: "商城路店",
          hospCode: "02",
          hospTel: "0371-55906231",
          hospAddr: "郑州蓝天健康体检门诊部",
          hospLogo: require("../assets/hosp02.png"),
        },
        {
          hospName: "纬四路店",
          hospCode: "03",
          hospTel: "0371-56533922",
          hospAddr: "郑州蓝天健康体检综合门诊部",
          hospLogo: require("../assets/hosp03.png"),
        },
      ],
      patientName: "",
      authorized: false,
    };
  },
  created() {
    
  },
  methods: {
    toHospital(hospInfo) {
      storage.session.set("hospInfo", JSON.stringify(hospInfo));
     
        this.$router.push({
          path: "/Index",
        });
      
    },
  },
};
</script>

<style lang="scss" scoped>
.hosp-choose {
  .title {
    font-size: 0.4rem;
    text-align: center;
    background: #fff;
    padding: 0.2rem;
  }
  .cell {
    font-size: 0.32rem;
    height: 2rem;
  }
}

/* 院区列表项 */
.hosp_item {
  display: flex;
}
.hosp_item > div:first-child {
  display: flex;
  align-items: center;
}
.hosp_item > div:first-child > img {
  width: 2rem;
  height: auto;
  size: auto;
  object-fit: cover; /* 覆盖整个元素区域，图片可能会被裁剪 */
}
.hosp_item > div:last-child {
  margin-left: 0.3rem;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
}
</style>
