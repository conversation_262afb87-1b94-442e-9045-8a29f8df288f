<template>
  <div>
    <div id="OrderDetails">
      <!-- top -->
      <div class="Information">
        <div class="InformationBox">
          <div class="InformationDiv">
            <div class="InformationTitle">
              <b>体检人信息</b>
            </div>
            <div class="InformationInput"></div>
          </div>
          <div class="InformationDiv">
            <div class="InformationTitle">
              <span>姓名</span>
            </div>
            <div class="InformationInput">{{ OrderList.name }}</div>
          </div>
          <div class="InformationDiv">
            <div class="InformationTitle">
              <span>证件号</span>
            </div>
            <div class="InformationInput">{{ OrderList.idCard }}</div>
          </div>
          <div class="InformationDiv">
            <div class="InformationTitle">
              <span>手机号</span>
            </div>
            <div class="InformationInput">{{ OrderList.tel }}</div>
          </div>
          <div class="InformationDiv">
            <div class="InformationTitle">
              <span>体检类型</span>
            </div>
            <div class="InformationInput" v-if="OrderList.type == 'person'">个人体检</div>
            <div class="InformationInput" v-else-if="OrderList.type == 'vehicle'">驾驶证体检</div>
            <div class="InformationInput" v-else-if="OrderList.type == 'staff'">入职体检</div>
            <div class="InformationInput" v-else>单位体检</div>
          </div>
          <div class="InformationDiv" v-show="TeamCompany" v-if="OrderList.type == 'group'">
            <div class="InformationTitle">
              <span>单位</span>
            </div>
            <div class="InformationInput">{{ OrderList.company_Name }}</div>
          </div>
          <div class="InformationDiv">
            <div class="InformationTitle">
              <span>体检地址</span>
            </div>
            <div class="InformationInput">{{ baseData.hospitalAddress }}</div>
          </div>
          <!-- <div class="InformationDiv">
                    <div class="InformationTitle">
                        <span>报到地点</span>
                    </div>
                    <div class="InformationInput">
                        个人体检登记处8号窗
                    </div>
          </div>-->
        </div>
      </div>

      <!-- 体检时间-->
      <div class="Information">
        <div class="InformationBox">
          <div class="InformationDiv" style="margin-top:.16rem;">
            <div class="InformationTitle">
              <span>体检时间</span>
            </div>
            <div class="InformationInput">
              <span>{{OrderList.begin_Time }}（{{ week }}）</span>
            </div>
          </div>
          <div class="InformationDiv" style="margin-top:.16rem;">
            <div class="InformationTitle">
              <span>报到时间</span>
            </div>
            <div class="InformationInput">
              <span>{{ OrderList.sumtime_Name }}</span>
            </div>
          </div>

          <div class="InformationDiv" v-if="OrderList.price && OrderList.price != null">
            <div class="InformationTitle">
              <span>总价格</span>
            </div>
            <div class="InformationInput" style="color: #D0021B;">
              <span style="font-size: .24rem;">￥</span>
              <span style="font-size: .4rem;">{{ OrderList.price }}</span>
            </div>
          </div>

        </div>
      </div>



      <!-- 套餐 -->
      <div class="SetMeal" v-if="clusIn && clusIn.length > 0">
        <van-collapse v-model="clusShowA">
          <van-collapse-item name="1">
            <template #title>
              <div class="SetTitle" style="margin-bottom: 0;">
                <img src="../../assets/SetMeal.png" alt="套餐项目" />
                <div>{{ OrderList.clus_Name }}（共{{ clusIn.length }}项）
                  <!-- <span class="SetTitle-price" v-if="OrderList.price && OrderList.price != null">￥{{ OrderList.price
                  }}</span> -->
                </div>
                <!-- <div v-else>套餐项目&nbsp;&nbsp;（共{{ clusIn.length }}项）<span class="SetTitle-price"></span></div> -->
              </div>
            </template>
            <div class="set-content">
              <div class="content-item" v-for="(item, index) in clusIn" :key="index">
                <span>{{ index + 1 }}、{{ item.comb_Name }}</span>
                <!-- <span class="item-price">￥{{ item.price }}</span> -->
              </div>
            </div>
          </van-collapse-item>
        </van-collapse>
      </div>

      <!-- 自选项目 -->
      <div class="SetMeal" v-if="clusOut && clusOut.length > 0">
        <van-collapse v-model="clusShowB">
          <van-collapse-item name="1">
            <template #title>
              <div class="SetTitle">
                <img src="../../assets/SetMeal.png" alt="自选项目" />
                <div>套餐外项目（共{{ clusOut.length }}项）
                  <!-- <span class="SetTitle-price">￥{{ OrderList.combPrice }}</span> -->
                </div>
              </div>
            </template>
            <div class="set-content">
              <div class="content-item" v-for="(item, index) in clusOut" :key="index">
                <!-- 有项目价格 -->
                <!-- <span>{{ index + 1 }}、{{ item.text }}</span> -->
                <!-- <span class="item-price">￥{{ item.price }}</span> -->
                <!-- 无价格 -->
                <span>{{ index + 1 }}、{{ item.comb_name }}</span>
              </div>
            </div>
          </van-collapse-item>
        </van-collapse>
      </div>



      <!-- 团检 -->
      <!-- <div class="footFixed" v-if="OrderList.state == 'F'">
        <div :class="[Cancel ? 'goto' : 'confirmType']" @click="cancelTeamOrder()">
          <span>取消预约</span>
        </div>
      </div> -->

      <div class="footFixed" v-if="OrderList.state == 'S'">
        <div :class="[Cancel ? 'goto' : 'confirmType']" @click="$router.push({
        name: 'PersonPayment',
        params: {
          order: JSON.stringify(OrderList)
        }
      })">
          <span>去缴费</span>
        </div>
      </div>

      <!-- @click="Ordertype(OrderList.type) -->

      <!-- <div class="footFixeds" v-else-if="isUsePay == true && OrderList.state == 'S'">
        <div :class="[Cancel ? 'confirms' : 'confirmTypes']" @click="PersonOrderUpdate">
          <span>取消订单</span>
        </div>
        <div :class="[Cancel ? 'confirms' : 'confirmTypes']" @click="payAmoutOrderAdd">
          <span>立即付款</span>
        </div>
      </div> -->
      <div class="footFixed" v-if="OrderList.state == 'export'">
        <div :class="[Cancel ? 'goto' : 'confirmType']" @click="GoCancel">
          <span>返回</span>
        </div>
      </div>
      <div class="footDiv"></div>
    </div>
  </div>
</template>
<script>
import { storage, ajax } from "../../common";
import apiUrls from "../../config/apiUrls";
import Vue from "vue";
import dayjs from 'dayjs';
export default {
  data() {
    return {
      baseData: Vue.prototype.baseData,
      show: false,
      InputNames: "",
      InputCards: "",
      InputTels: "",
      InputCompany: "",
      TeamCompany: true, //单位是否显示show
      index: "", //传过来的索引值
      date: "",
      week: "",
      price: "",
      PersonalSpan: "",
      img: "",
      // list
      OrderList: [],
      // Tab按钮样式
      BtnState: true,
      // Tab页文本显示
      TabText: true,
      // 体检须知文本
      NoticeDiv: [
        {
          NoticeText:
            "提前预约，为了成功提交订单，您最晚要在体检前1天（具体以网站公示的号源情况为准）预订，请尽早预订。"
        },
        {
          NoticeText:
            "如入职单位对体检项目有特殊要求，请于体检日在我中心前台进行具体咨询与调整。"
        },
        {
          NoticeText: "以上所有解释权归本健康体检中心所有！"
        }
      ],
      // 项目套餐
      detailed: [],

      clusIn: [],//套餐内项目
      clusOut: [],//套餐外项目
      clusShowA: [],
      clusShowB: [],
      otherCombs: [],//体检系统已加项的
      clusShowC: [],




      // 套餐长度
      SetMealLength: 0,
      Cancel: true,
      CancelText: "取消预约",
      footFixed: false,
      week: "",
      isUsePay: Vue.prototype.baseData.IsUsePay || false
    };
  },

  created() {
    this.OrderList = JSON.parse(storage.session.get("ItemList"));
    var WeekDay = ["日", "一", "二", "三", "四", "五", "六"];
    var date = new Date(this.OrderList.begin_Time);
    this.week = "星期" + WeekDay[date.getDay()];
    if (this.OrderList.choose_comb_code) {
      this.clusOut = JSON.parse(this.OrderList.choose_comb_code);
    }
    this.GetItemCombList(this.OrderList.clus_Code);
    this.OrderList.begin_Time=dayjs(this.OrderList.begin_Time).format("YYYY-MM-DD")

  },
  methods: {
    //获取套餐详细项目
    GetItemCombList(clus_Code) {

      var that = this;
      var pData = {
        comb_code: clus_Code
      }
      ajax.post(apiUrls.GetItemCombList, pData, { nocrypt: true }).then(r => {
        var data = r.data.returnData;
        if (r.data.success) {
          this.clusIn = data
          this.detailed = data;
        }
        else {
          this.detailed = [];
        }
      }).catch(e => {
        alert("系统繁忙！请稍后再试");
      })
    },

    // 套餐简介
    btnIntroduction: function () {
      this.BtnState = !this.BtnState;
      this.TabText = true;
    },
    // 体检须知
    btnKnow: function () {
      this.BtnState = !this.BtnState;
      this.TabText = false;
    },
    // 返回上一页
    GoCancel: function () {
      window.history.go(-1);
    },
    SetWeek: function (date) {
      var a = ["日", "一", "二", "三", "四", "五", "六"];
      var week = a[date];
      return week;
    },
    // WxConfig() {
    //   var url = "";
    //   ajax
    //     .post(apiUrls.getWxConfig, { params: { url: url } })
    //     .then(r => {
    //       var data = r.data.returnData;
    //       wx.config({
    //         debug: false,
    //         appId: data.appid,
    //         timestamp: data.timeStamp,
    //         nonceStr: data.nonceStr,
    //         signature: data.Signature,
    //         jsApiList: ["checkJsApi", "chooseWXPay"]
    //       });
    //     })
    //     .catch(e => {
    //       var d = JSON.stringify(d);
    //       Toast(d);
    //     });
    // },
    Ordertype(type) {
      var that = this;
      switch (type) {
        case "person":
          that.cancelPersonOrder(type);
          break;
        case "question":
          that.cancelPersonOrder(type);
          break;
        case "vehicle":
          that.cancelPersonOrder(type);
          break;
        case "staff":
          that.cancelPersonOrder(type);
          break;
      }
    },
    //取消个人订单
    cancelPersonOrder(type) {
      var that = this;
      let bnum = that.baseData.PersonsumStartTiem - 1;
      var time = new Date(new Date(that.OrderList.begin_Time) - bnum * 24 * 60 * 60 * 1000); //前一天
      var deadTime = time.getFullYear() + "-" + (time.getMonth() + 1 < 10 ? "0" + (time.getMonth() + 1) : time.getMonth() + 1) + "-" + time.getDate() + " " + "00" + ":" + "00"; //前bnum天
      var yesterdayTime = deadTime.replace(/-/g, "/");

      var d = new Date();
      var nowDate = d.getFullYear() + "/" + (d.getMonth() + 1) + "/" + d.getDate() + " " + d.getHours() + ":" + d.getMinutes(); //体检日期大于当前日期不可取消   当前时间四点半
      if (Date.parse(yesterdayTime) > Date.parse(nowDate)) {
        var cancle = confirm("确认撤销订单？");
        if (cancle) {
          var pData = {
            id: that.OrderList.id,
            idCard: that.OrderList.idCard,
            type: type
          };
          that.show = true;
          //不需要支付的方法，但是会发送消息模板
          // ajax.post(apiUrls.PersonOrderNoPayRefund, pData, { nocrypt: true }).then(r => {
          //   //var data = r.data.returnData;
          //   if (r.data.success) {
          //     that.show = true;
          //     alert("撤销成功");
          //     that.$router.go(-1);
          //   } else {
          //     alert(r.data.returnMsg);
          //   }
          // }).catch(e => {
          //   that.show = false;
          //   alert("系统繁忙！请稍后再试");
          // })

          // return;
          // if (that.isUsePay == false) {
          //   //不需要支付的方法，但是会发送消息模板
          //   ajax.post(apiUrls.PersonOrderNoPayRefund, pData, { nocrypt: true }).then(r => {
          //     var data = r.data.returnData;
          //     if (r.data.success) {
          //       that.show = true;
          //       alert("撤销成功");
          //       that.$router.go(-1);
          //     } else {
          //       alert(r.data.returnMsg);
          //     }
          //   }).catch(e => {
          //     that.show = false;
          //     alert("系统繁忙！请稍后再试");
          //   })

          //   return;
          // }
          //需要支付的方法

          
          ajax.post(apiUrls.PersonOrderRefund, pData, { nocrypt: true }).then(r => {
            var data = r.data.returnData;
            if (r.data.success) {
              that.show = true;
              alert("撤销成功");
              that.$router.go(-1);
            } else {
              alert(r.data.returnMsg);
            }
          }).catch(e => {
            that.show = false;
            alert("系统繁忙！请稍后再试");
          })
        } else {
          return;
        }
      }
      else {
        alert("已过有效取消时间，无法取消！");
        return;
      }
    },

    //取消团体订单
    cancelTeamOrder() {
      var that = this;
      let bnum = that.baseData.TeamsumStartTiem - 1;
      var time = new Date(new Date(that.OrderList.begin_Time) - bnum * 24 * 60 * 60 * 1000); //前一天
      var deadTime = time.getFullYear() + "-" + (time.getMonth() + 1 < 10 ? "0" + (time.getMonth() + 1) : time.getMonth() + 1) + "-" + time.getDate() + " " + "00" + ":" + "00"; //前bnum天
      var yesterdayTime = deadTime.replace(/-/g, "/");

      var d = new Date();
      var nowDate = d.getFullYear() + "/" + (d.getMonth() + 1) + "/" + d.getDate() + " " + d.getHours() + ":" + d.getMinutes(); //体检日期大于当前日期不可取消   当前时间四点半
      if (Date.parse(yesterdayTime) > Date.parse(nowDate)) {
        var cancle = confirm("确认撤销订单？");
        if (cancle) {
          var pData = that.OrderList;
          that.show = true;
          ajax.post(apiUrls.TeamOrderCancel, pData, { nocrypt: true }).then(r => {
            var data = r.data.returnData;
            if (r.data.success) {
              that.show = false;
              alert("成功取消！");
              that.$router.go(-1);
            }
            else {
              that.show = false;
              alert("撤销失败！请稍后再试");
            }
          }).catch(e => {
            that.show = false;
            alert("系统繁忙！请稍后再试");
          })

        } else {
          return;
        }
      }
      else {
        alert("已过有效取消时间，无法取消！");
        return;
      }
    },

    //待支付订单撤销
    PersonOrderUpdate() {
      var that = this;
      that.show = true;
      var pData = {
        id: that.OrderList.id,
        idCard: that.OrderList.idCard
      }
      ajax.post(apiUrls.PersonOrderCancel, pData, { nocrypt: true }).then(r => {
        var data = r.data.returnData;
        if (r.data.success) {
          alert(r.data.returnMsg);
          this.$router.go(-1);
        }
        else { alert(r.data.returnMsg); }
      }).catch(e => {
        alert("系统繁忙！请稍后再试");
      })
    },

    //待支付订单支付
    // payAmoutOrderAdd() {
    //   this.show = true;
    //   var pData = this.OrderList;
    //   ajax.post(apiUrls.PersonOrderPay, pData, { nocrypt: true }).then(r => {
    //     var data = r.data.returnData;
    //     WeixinJSBridge.invoke('getBrandWCPayRequest', {
    //       "appId": data.appid, //公众号名称，由商户传入
    //       "timeStamp": data.timeStamp, //时间戳
    //       "nonceStr": data.nonceStr, //随机串
    //       "package": data.package,//扩展包
    //       "signType": "MD5", //微信签名方式:MD5
    //       "paySign": data.paySign //微信签名
    //     }, function (res) {
    //       switch (res.err_msg) {
    //         case 'get_brand_wcpay_request:cancel':
    //           alert("取消支付");
    //           break;
    //         case 'get_brand_wcpay_request:fail':
    //           alert("支付失败，可能的原因：签名错误、未注册APPID、项目设置APPID不正确、注册的APPID与设置的不匹配、其他异常等。");
    //           break;
    //         case 'get_brand_wcpay_request:ok':
    //           alert("支付成功");
    //           setTimeout(() => {
    //             this.$router.go(-1);
    //           }, 1000)
    //           break;
    //       }
    //     });
    //   }).catch(e => {
    //     alert("系统繁忙！请稍后再试");
    //     return;
    //   })
    // }

  }
};




</script>
<style lang="scss" scoped>
#OrderDetails .Information {
  width: 100%;
  /* height: 4.35rem; */
  background: white;
  position: relative;
  top: 0;
  left: 0;
  box-sizing: border-box;
}

.Information .InformationBox {
  width: 92%;
  height: 100%;
  margin: 0 auto;
  /* border: 1px solid; */
}

.InformationBox .InformationDiv {
  width: 100%;
  height: 0.87rem;
  display: flex;
  border-top: 1px solid #dfe3e9;
  box-sizing: border-box;
  font-size: 0.28rem;
}

.InformationBox div:nth-child(1) {
  border: 0;
}

.InformationDiv .InformationTitle {
  width: 30%;
  height: 100%;
  display: flex;
  align-items: center;
  color: #4a4a4a;
}

.InformationDiv .InformationInput {
  width: 70%;
  height: 100%;
  display: flex;
  color: #9b9b9b;
  justify-content: flex-end;
  align-items: center;
}

/*  logo 套餐*/
.detailsDiv {
  width: 100%;
  background: white;
  box-sizing: border-box;
}

.detailsBox {
  margin-top: 0.16rem;
}

.detailsDiv .detailsTop {
  width: 92%;
  height: 1.24rem;
  margin: 0 auto;
  font-size: 0.28rem;
  display: flex;
  justify-content: center;
  align-items: center;
}

.detailsTop .TopImg {
  width: 1rem;
  height: 1rem;
  display: flex;
  justify-content: center;
  align-items: center;
}

.detailsTop .TopTitle {
  width: calc(100% - 2.16rem);
  height: 100%;
  font-size: 0.36rem;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.TopTitle span {
  margin-left: 0.15rem;
}

.detailsTop .TopBtn {
  width: 1.4rem;
  height: 100%;
  color: #ffffff;
  letter-spacing: -0.04rem;
  display: flex;
  align-items: center;
}

.TopBtn div {
  width: 1.4rem;
  height: 0.48rem;
  background: #6a9be4;
  border-radius: 0.04rem;
  text-align: center;
  line-height: 0.48rem;
}

/* Tab */
.TabDiv {
  border-top: 0.02rem solid #dfe3e9;
}

.detailsDiv .detailsTab {
  width: 92%;
  margin: 0 auto;
}

.detailsTab .TabBtn {
  width: 100%;
  height: 0.64rem;
  font-size: 0.28rem;
  display: flex;
  margin-top: 0.24rem;
}

.TabBtn .BtnBlueL {
  width: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #6a9be4;
  color: white;
  border-radius: 2px 0 0 2px;
}

.TabBtn .BtnWhiteL {
  width: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: white;
  color: #6a9be4;
  border: 1px solid #6a9be4;
  border-radius: 2px 0 0 2px;
}

.TabBtn .BtnBlueR {
  width: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #6a9be4;
  color: white;
  border-radius: 0 2px 2px 0;
}

.TabBtn .BtnWhiteR {
  width: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: white;
  color: #6a9be4;
  border: 1px solid #6a9be4;
  border-radius: 0 2px 2px 0;
}

.detailsTab .TabText {
  width: 100%;
  min-height: 1rem;
  font-size: 0.28rem;
  color: #9b9b9b;
  letter-spacing: -0.01px;
  line-height: 0.44rem;
  margin-top: 0.2rem;
}

.detailsTab .bottomDiv {
  width: 100%;
  height: 0.3rem;
}

.TabSpan {
  margin-top: 0.08rem;
}

.TabText .NoticeDiv {
  width: 100%;
  margin-top: 0.08rem;
  display: flex;
}

.NoticeDiv .NoticeNumber {
  width: 0.28rem;
}

.NoticeDiv .NoticeText {
  width: calc(100% - 0.28rem);
}

#OrderDetails .SetMeal {
  width: 100%;
  font-size: 0.28rem;
  color: #4a4a4a;
  box-sizing: border-box;
  letter-spacing: -0.01px;
  margin-top: 0.16rem;
}

.SetTitle {
  // width: 100%;
  // height: 1rem;
  display: flex;
  align-items: center;
}

.SetTitle img {
  margin-right: 0.1rem;
}

.SetTitle-price {
  color: #d0021b;
}

.set-content {
  font-size: 0.3rem;
  color: #b7b7b7;
}

.content-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.12rem;
}

.tips {
  width: 98%;
  font-size: 0.28rem;
  color: #D0021B;
  // margin-left: 0.2rem;
  // margin-top: 0.2rem;
  margin: 0.2rem;
}

// #OrderDetails .SetMeal {
//   width: 100%;
//   font-size: 0.28rem;
//   color: #4a4a4a;
//   box-sizing: border-box;
//   letter-spacing: -0.01px;
//   margin-top: 0.16rem;
// }

// .SetMeal .SetMealBottom {
//   border-bottom: 1px solid #dfe3e9;
// }

// .SetMeal .SetMealA {
//   width: 92%;
//   height: 0.84rem;
//   margin: 0 auto;
//   display: flex;
//   align-items: center;
// }

// .SetMealA .SetMealImg {
//   width: 0.4rem;
//   height: 0.4rem;
//   display: flex;
//   justify-content: center;
//   align-items: center;
// }

// .SetMealImg img {
//   margin-top: 3px;
// }

// .SetMealA span {
//   margin-left: 0.1rem;
// }

// .SetMeal .detailed {
//   width: 92%;
//   margin: 0 auto;
//   border-bottom: 1px solid #dfe3e9;
// }

// .detailed .detailedTitle {
//   width: 100%;
//   height: 0.8rem;
//   display: flex;
//   align-items: center;
//   font-weight: bold;
// }

// .detailed .detailedText {
//   color: #535151;
//   letter-spacing: -0.01px;
//   line-height: 0.44rem;
// }

// .detailed .detailedBottom {
//   width: 100%;
//   height: 0.24rem;
// }

#OrderDetails .footFixed {
  width: 100%;
  height: 1.36rem;
  position: fixed;
  left: 0;
  bottom: 0;
  display: flex;
  font-size: 0.24rem;
  background: white;
  border-top: 1px solid #dfe3e9;
}

.footFixed .goto {
  width: 92%;
  height: 0.96rem;
  background: #f2ad3b;
  border-radius: 5px;
  margin: 0.2rem auto;
  font-size: 0.32rem;
  color: #ffffff;
  letter-spacing: -0.02px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.footFixed .confirmType {
  width: 92%;
  height: 0.96rem;
  background: #c9c9c9;
  border-radius: 5px;
  margin: 0.2rem auto;
  font-size: 0.32rem;
  color: #ffffff;
  letter-spacing: -0.02px;
  display: flex;
  justify-content: center;
  align-items: center;
}

#OrderDetails .footFixeds {
  width: 100%;
  height: 1.36rem;
  position: fixed;
  left: 0;
  bottom: 0;
  display: flex;
  font-size: 0.24rem;
  background: white;
  border-top: 1px solid #dfe3e9;
  justify-content: space-between;
  align-items: center;
}

.footFixeds .confirms {
  width: 40%;
  height: 0.96rem;
  background: #f2ad3b;
  border-radius: 5px;
  margin: 0.2rem auto;
  font-size: 0.32rem;
  color: #ffffff;
  letter-spacing: -0.02px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.footFixeds .confirmTypes {
  width: 40%;
  height: 0.96rem;
  background: #c9c9c9;
  border-radius: 5px;
  margin: 0.2rem auto;
  font-size: 0.32rem;
  color: #ffffff;
  letter-spacing: -0.02px;
  display: flex;
  justify-content: center;
  align-items: center;
}

#OrderDetails .footDiv {
  width: 100%;
  height: 1.16rem;
}

.Notice {
  width: 100%;
  height: 0.84rem;
  background: rgba(106, 155, 228, 0.2);
  display: flex;
}

.Notice div:nth-child(1) {
  width: 50%;
  height: 0.84rem;
  display: flex;
  align-items: center;
}

.Notice div:nth-child(1) img {
  margin-left: 0.24rem;
}

.Notice div:nth-child(1) span {
  margin-left: 0.12rem;
  font-size: 0.28rem;
  letter-spacing: -0.01px;
}

.Notice div:nth-child(2) {
  width: 50%;
  height: 0.84rem;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-right: 0.24rem;
}

.Notice div:nth-child(2) img {
  width: 0.16rem;
  height: 0.32rem;
}
</style>