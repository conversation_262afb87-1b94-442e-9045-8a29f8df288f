<template>
    <div>
        <div id="MyAppointment">
        <!-- <div @click="fhindex" style="font-size:20px;color:blue;position:fixed;top:10px;left:5px;">返回首页</div> -->
         <div   class="historyBtn">
                 <van-button @click="fhindex" icon="wap-home" type="primary" size="small" color="linear-gradient(to right, rgb(121, 187, 255), rgb(84, 135, 212))">返回首页</van-button>
             </div>
        <div class="OrderList" v-for="(item,index) in OrderList" :key='index'>
            <div class="OrderTime">
                <span>{{item.created_Time}}</span>
            </div>
            <div class="OrderContent">
                <div class="OrderTitle">
                    <div class="titleImg">
                        <img src="../../assets/outCall3.png" alt="套餐详情" />
                    </div>
                    <div class="CardText"><span>{{item.clus_Name}}</span></div>
                </div>
                <div class="OrderNews" style=" border-top:1px solid #DFE3E9;">
                    <span>姓名</span>
                    <span style="color: #4A4A4A;">{{item.name}}</span>
                </div>
                <div class="OrderNews" style=" border-top:1px solid #DFE3E9;">
                    <span>体检号</span>
                    <span style="color: #4A4A4A;">{{item.regno}}</span>
                </div>

                <div class="OrderNews" style=" border-top:1px solid #DFE3E9;" v-if="item.tj_date">
                    <span>体检日期</span>
                    <span style="color: #4A4A4A;">{{item.tj_date}}</span>
                </div>

                <div class="OrderNews" style=" border-top:1px solid #DFE3E9;">
                    <span>体检套餐</span>
                    <span style="color: #4A4A4A;">{{item.clus_Name}}</span>
                </div>

                <div class="OrderNews" v-if="item.price!=null">
                    <span>套餐价格</span>
                    <span style="color: #D0021B;">
                        <span style="font-size: .24rem;">￥</span>
                        <span style="font-size: .4rem;">{{item.price}}</span>
                    </span>
                </div>
                
                <div class="OrderNews" style=" border-top:1px solid #DFE3E9;">
                    <span>缴费状态</span>
                    <span style="color: red;" v-if="item.pay_flag=='F'">待支付</span>
                    <span style="color: red;" v-if="item.pay_flag=='EING'">支付中</span>
                    <span style="color: red;" v-if="item.pay_flag=='TJServeERR'">已退款</span>
                    <span style="color: red;" v-if="item.pay_flag=='RefundERR'">退款失败</span>
                     <span style="color: red;" v-if="item.pay_flag=='T'">已支付</span>
                </div>
                <!-- <div class="OrderNews" v-if="item.state=='E'">
                    <span>取消信息</span>
                    <span style="color:#D0021B;">{{item.errmsg}}</span>
                </div>
                <div class="SeeBtn" @click='SeeBtn(item)'>
                    <span> 查 看 详 情</span>
                </div> -->
            </div>

        </div>
        <div v-show="clusFlag" class="clusFlag">
            <div><img src="../../assets/kong.png" /></div>
            <span style="margin-top: 10px;color: dimgray;">暂无缴费记录</span>
        </div>
    </div>
    </div>
</template>
<script>
import {ajax,storage} from '../../common'
import apiUrls from '../../config/apiUrls'
import{Toast,Icon,Button} from 'vant'
import wx from 'weixin-jsapi';
import Vue from 'vue';
import router from '../../router';

export default {
data(){
    return{
        OrderList: [],
        clusFlag: false,
        show:false,
        isUsePay:Vue.prototype.baseData.IsUsePay||false
    }
},
created(){
    var openid=storage.cookie.get("openid")
    this.GetPayRecordList(openid);
},
methods:{
    fhindex(){
       router.push({
           name:'index'
       })
    },
    GetPayRecordList(openid){
        this.show=true;
        var pData={
            openid:openid
        }
        ajax.post(apiUrls.GetPayRecordList,pData,{nocrypt:true}).then(r=>{
            this.show=false;
            if(r.data.success){
                if(r.data.returnData.length!=0){
                    this.OrderList=r.data.returnData;
                    return;
                }
                this.clusFlag=true;
                return;
            }
            this.clusFlag=true;
            Toast(r.data.returnMsg);
            return;
        }).catch(e=>{
            this.show=false;
            Toast(e.data.returnMsg);
            return;
        })       
    },

    SeeBtn(item){
        storage.session.set('ItemList',JSON.stringify(item))
        this.$router.push({
            path:'/OrderDetails'
        })
    }
}    
}
</script>
<style lang="scss">
#MyAppointment{
     .OrderList:nth-child(2){
             margin-top: 1rem;
          }
}
#MyAppointment .OrderList {
            width: 92%;
            /*height: 5.22rem;*/
            margin: .26rem auto;
            font-size: .28rem;
            box-sizing: border-box;
        }

        .OrderList .OrderTime {
            width: 100%;
            height: .4rem;
            color: #9B9B9B;
            letter-spacing: -0.02px;
            text-align: center;
        }

        .OrderList .OrderContent {
            width: 100%;
            /*height: 4.66rem;*/
            margin-top: .16rem;
            background: white;
            border-radius: .1rem;
            -moz-box-shadow: 2px 2px 5px #333333;
            -webkit-box-shadow: 2px 2px 5px #333333;
            box-shadow: 2px 2px 5px #333333;
            box-sizing: border-box;
        }

        .OrderContent .OrderTitle {
            width: 96%;
            height: 1rem;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-left: 4%;
        }

        .OrderTitle .titleImg {
            width: .56rem;
            height: .56rem;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .OrderTitle .CardText {
            width: calc(100% - .76rem);
            height: 1rem;
            display: flex;
            align-items: center;
            margin-left: .2rem;
            font-size: .32rem;
            color: #4A4A4A;
            letter-spacing: -0.02px;
            font-weight: 600;
        }

        .OrderTitle .PersonalSpan {
            width: 100%;
            height: .9rem;
            color: #9B9B9B;
            letter-spacing: -0.01px;
            line-height: .44rem;
            /* 超过两行省略号 */
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .OrderContent .OrderNews {
            width: 92%;
            height: .82rem;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: #4A4A4A;
            letter-spacing: -0.01px;
            border-top: 1px solid #DFE3E9;
        }

        .OrderContent div:nth-child(1) {
            border: 0;
        }

        .OrderContent .SeeBtn {
            width: 100%;
            height: .99rem;
            background: rgba(106,155,228,0.10);
            display: flex;
            justify-content: center;
            align-items: center;
            color: #6A9BE4;
        }
        .clusFlag {
            display: flex;
            flex-direction: column;
            align-items: center;
            font-size: 0.3rem;
            height: 10rem;
            justify-content: center;
        }
        .historyBtn{
   width: 100%;
   height: .85rem; 
   font-size: .36rem;
   position: fixed;
   top: 0;
   left:0;
   display: flex;
   align-items: center;
   background: white;
   box-shadow: 1px 1px 2px #ccc;
   .van-button--small{
       margin-left: 4%;
   }
}
</style>