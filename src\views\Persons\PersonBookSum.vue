<template>
        <div id="all">
        <div id="calendar">
            <div class="month">
                <div class="prevMonth" @click="prevMonth" v-show="pre">
                    上一月
                </div>
                <div class="prevMonth_" v-show="pre_"></div>
                <div class="year-month">
                    <span class="choose-year">{{ currentDateStr }}</span>
                </div>
                <div class="nextMonth" @click="nextMonth">
                    下一月
                </div>
            </div>
            <div class="weekdays">
                <div class="week-item" v-for="item of weekList" :key="item">{{ item }}</div>
            </div>
            <div class="calendar-inner">
                <div class="calendar-item" v-for="(item, index) of calendarList" :key="index" v-bind:class="[item.disable ? 'disabled' : '']">
                    <div @click="item.Thing ===''?'':(matchDate(item.ThingName)?dayClick(item.date,item.ThingName,index):'')"
                         :class="ClassKey===calendarList[index].value?'chooseDay':''">
                        <div class="calendarDate " v-bind:class="switchHaoyuanClass(item.ThingName)">{{item.date}}</div>
                        <div class="calendarThing" v-bind:class="switchHaoyuanClass(item.ThingName)">{{item.Thing}}</div>
                    </div>
                </div>
            </div>   
        </div>
    
        <div class="btnTime" v-show="state" >
           <!-- 不需要号源时段的项目注释掉-->
            <!-- <div :class="timeStateA?'timeNow':'timeOld'" @click="timeBtn(1)">8:00-9:00</div> -->
            <div :class="sumItem.person_Surplus===0?'timeNow':'timeOld'" @click="sumItem.person_Surplus===0?'':timeBtn(index,sumItem.sumtime_Name,sumItem.sumtime_Code)" v-for="(sumItem,index) in sumtimeList" :key="index">
                <span :class="TimeSpan===index?'TimeSpan':'TimeSpanTwo'">{{sumItem.sumtime_Name}}</span>
            </div>
        </div> 
        <div class="confirm" @click="ToPersonalOrder">
            <span>日期确认</span>
        </div>
    </div>
</template>
<script>
import apiUrls from '../../config/apiUrls'
import {ajax,storage} from '../../common'
import { Toast } from 'vant'
export default {
    data(){
        return{
            ClassKey: "", 
            years: "", 
            months: "", 
            pre: false, 
            pre_: true, 
            week: 0, 
            current: {}, 
            calendarList: [], 
            shareDate: new Date(), 
            weekList: ["日", "一", "二", "三", "四", "五", "六"], 
            timeStateA:true,//时间段选择状态
            timeStateP:false,//时间段选择状态
            state:false,
            sumtimeList:[],
            TimeSpan:'',
            sumtimeName:'',
            sumtime_Code:'',
            type:"",
            hospInfo:{},
        }
    },
    computed: {
       currentDateStr() {
        let {
            year,
            month
        } = this.current;                   
        return `${year}-${this.pad(month + 1)}`;
        }
    },
    mounted() {
         this.init();
    },
    created() {
        this.hospInfo=JSON.parse(storage.session.get("hospInfo"));
    },
    methods:{
        //时间段选择判断
        timeBtn(index,timeNmae,sumtime_Code){
           var that=this;
           that.TimeSpan=index;
           that.sumtimeName=timeNmae;
           that.sumtime_Code=sumtime_Code;
        },

        init:function () {
            this.type="person"
            // 初始化当前时间
            this.setCurrent();
            this.calendarCreator();
           
        },
        // 判断当前月有多少天
        getDaysByMonth: function (year, month) {
            return new Date(year, month + 1, 0).getDate();
        },
        getFirstDayByMonths: function (year, month) {
            return new Date(year, month, 1).getDay();
        },
        getLastDayByMonth: function (year, month) {
            return new Date(year, month + 1, 0).getDay();
        },
        // 对小于 10 的数字，前面补 0
        pad: function (str) {
            return str < 10 ? `0${str}` : str;
        },
        // 点击上一月
        prevMonth: function () {
            this.current.month--;

            // 因为 month的变化 会超出 0-11 的范围， 所以需要重新计算
            this.correctCurrent();
            // 生成新日期
            this.calendarCreator();
        },
        // 点击下一月
        nextMonth: function () {
            this.current.month++;
            // 因为 month的变化 会超出 0-11 的范围， 所以需要重新计算
            this.correctCurrent();
            // 生成新日期
            this.calendarCreator();
        },
        // 格式化时间，与主逻辑无关
        stringify: function (year, month, date) {
            let str = [year, this.pad(month + 1), this.pad(date)].join("-");
            return str;
        },
        // 设置或初始化 current
        setCurrent: function (d = new Date()) {
            let year = d.getFullYear();
            let month = d.getMonth();
            let date = d.getDate();
            this.current = {
                year,
                month,
                date
            };
        },
        // 修正 current
        correctCurrent: function () {
            let {
                year,
                month,
                date
            } = this.current;

            let maxDate = this.getDaysByMonth(year, month);
            // 预防其他月跳转到2月，2月最多只有29天，没有30-31
            date = Math.min(maxDate, date);

            let instance = new Date(year, month, date);
            this.setCurrent(instance);
        },
        // 生成日期
        calendarCreator: function () {
            // 一天有多少毫秒
            const oneDayMS = 24 * 60 * 60 * 1000;

            let list = [];
            let {
                year,
                month
            } = this.current;

            // 当前月份第一天是星期几, 0-6
            let firstDay = this.getFirstDayByMonths(year, month);
            // 填充多少天 firstDay-1则为周一开始，
            let prefixDaysLen = firstDay === 0 ? 0 : firstDay;
            // 毫秒数
            let begin = new Date(year, month, 1).getTime() - oneDayMS * prefixDaysLen;

            // 当前月份最后一天是星期几, 0-6
            let lastDay = this.getLastDayByMonth(year, month);
            // 填充多少天， 和星期的排放顺序有关
            let suffixDaysLen = lastDay === 0 ? 0 : 6 - lastDay;
            // 毫秒数
            let end =
                new Date(year, month + 1, 0).getTime() + oneDayMS * suffixDaysLen;

            while (begin <= end) {
                // 享元模式，避免重复 new Date
                this.shareDate.setTime(begin);
                let year = this.shareDate.getFullYear();
                let curMonth = this.shareDate.getMonth();
                let date = this.shareDate.getDate();
                let week = this.shareDate.getDay(); // 当前周几
                list.push({
                    year: year,
                    month: curMonth,
                    date: date,
                    Thing: "待开",
                    week: week,
                    ThingName:"",
                    disable: curMonth !== month,
                    value: this.stringify(year, curMonth, date)
                });

                begin += oneDayMS;
            }
            this.calendarList = list;
            this.judgeHaoyuan();
        },
                
        judgeHaoyuan: function () {               
            var that = this;
            var nowDate = this.stringify(
                new Date().getFullYear(),
                new Date().getMonth(),
                new Date().getDate()
            );

            console.log(that.hospInfo.hospCode)
            var pData={
                type:that.type,
                start:that.baseData.PersonsumStartTiem,
                end:that.baseData.PersonsumendTime,
                hospCode:that.hospInfo.hospCode
            }
            ajax.post(apiUrls.GetPersonSumList,pData,{nocrypt:true}).then(r=>{
                var personList = r.data.returnData;
                for (var i = 0; i < that.calendarList.length; i++) {
                    that.prevMonthIconShow();                   
                    if (nowDate > that.calendarList[i].value) {
                        that.calendarList[i].Thing = "";
                        continue;                     
                    }                                 
                    for (var j = 0; j < personList.length; j++) {
                        if (that.calendarList[i].value == personList[j].person_Date) { 
                            if(personList[j].person_Flag=="T") {
                                that.calendarList[i].Thing = "休假";
                                that.calendarList[i].ThingName = "休假";
                                break;
                            }
                            var person_Surplus=personList[j].person_Surplus;
                            if (personList[j].person_Sum<= 0) {
                                that.calendarList[i].Thing = "待开";
                                that.calendarList[i].ThingName = "待开";
                                break;
                            }
                            if ( person_Surplus> 10) {
                                that.calendarList[i].Thing = "余" + person_Surplus + "人";
                                that.calendarList[i].ThingName = "充足";
                            } 
                            else if (person_Surplus == 0) {
                                that.calendarList[i].Thing = "约满";
                                that.calendarList[i].ThingName = "约满";
                            }
                            else if (person_Surplus <= 10)
                            {
                                that.calendarList[i].Thing = "余" + person_Surplus + "人";
                                that.calendarList[i].ThingName = "紧张";
                            }
                        }
                    }
                } 
                }).catch(e=>{
                alert("服务异常！请稍等");
                return;
            })
        },
        // 根据日期显示添加类名
        switchHaoyuanClass: function (value) {
            switch (value) {
                case "充足":
                    return "haoyuan-green";
                    break;
                case "约满":
                    return "haoyuan-red";
                    break;
                case "休假":
                    return "haoyuan-red";
                    break;
                case "紧张":
                    return "haoyuan-adequate";
                    break;
                case "":
                    return "";
                    break;
            }
        },
        matchDate: function (date) {
            if (date == "充足" || date=="紧张") {
                return true;
            } else if (date === "约满" || "待开" || "休假"|| "") {
                return false;
            }
        },
        prevMonthIconShow: function () {
            if (this.current.year == new Date().getFullYear()) {
                if (this.current.month > new Date().getMonth()) {
                    this.pre = true;
                    this.pre_ = false;
                } else {
                    this.pre = false;
                    this.pre_ = true;
                }
            } else if (this.current.year > new Date().getFullYear()) {
                this.pre = true;
                this.pre_ = false;
            }
        },
        dayClick: function (date, key, index) {
            if (key=="充足" || key=="紧张") {
                this.years = this.current.year;
                this.months = this.current.month + 1;
                this.ClassKey =this.years +"-" +(this.months < 10 ? "0" + this.months : this.months) +"-" +(date < 10 ? "0" + date : date);
                this.week = this.calendarList[index].week;
                var pData={
                    date_Time:this.ClassKey,
                    type:this.type
                }
                //不需要号源时段的项目注释掉
                this.TimeSpan='';
                var that=this;
                ajax.post(apiUrls.GetSumTimeList,pData,{nocrypt:true}).then(r=>{                  
                    if(r.data.success)
                    {
                        that.state=true;                       
                        var List=r.data.returnData;
                        that.sumtimeList=List;
                    }
                }).catch(e=>{
                    Toast('系统异常！请联系管理员');
                    return;
                })
                //
                return true;
            } else {
                this.ClassKey = false;
                return false;
            }
        },
        ToPersonalOrder: function () {
            if (this.ClassKey == '' && this.ClassKey == false) {
                Toast('请选择体检日期');
                return;
            }
            else if(this.sumtimeName==''){
                Toast('请选择报到时间');
                return;
            }
            else{
                var calendarDay = {
                    date: this.ClassKey,
                    week: this.week,
                    sumtimeName:this.sumtimeName,
                    sumtime_Code:this.sumtime_Code
                }
                storage.session.set('calendarDay', JSON.stringify(calendarDay));
                this.$router.push({
                    path:'/PersonOrders'
                })
            
            }
        }
    }
}
</script>
<style lang="scss" scoped>
#all {
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0;
            left: 0;
        }

        #calendar {
            width: 100%;
            height: 7.36rem;
            // position: absolute;
            top: 0;
            left: 0;
            background: white;
        }

        .month {
            height: 0.88rem;
            line-height: 0.88rem;
            text-align: center;
            font-size: 0.28rem;
            /* text-transform: uppercase; */
            color: #4A4A4A;
            background-color: #fff;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: space-between;
            letter-spacing: -0.01px;
            /* position: relative; */
        }

        .prevMonth {
            width: 1.2rem;
            height: 0.88rem;
            line-height: 0.88rem;
            margin-left: 0.2rem;
            color: #6A9BE4;
        }

        .prevMonth_ {
            width: 1.2rem;
            height: 0.88rem;
            margin-left: 0.2rem;
        }

        .nextMonth {
            width: 1.2rem;
            height: 0.8rem;
            line-height: 0.88rem;
            margin-right: 0.2rem;
            color: #6A9BE4;
        }

        .weekdays {
            height: 0.48rem;
            line-height: 0.48rem;
            margin: 0;
            padding: 0 2%;
            /* background-color: #00b8ec; */
            display: flex;
            flex-wrap: wrap;
            /* color: #ffffff; */
            color: #9B9B9B;
            font-size: .28rem;
            justify-content: space-around;
        }

            .weekdays .week-item {
                display: inline-block;
                width: 13.6%;
                text-align: center;
                font-size: 0.28rem;
            }

        .calendar-inner {
            margin-left: 0.2rem;
        }

        .calendar-item {
            box-sizing: border-box;
            float: left;
            width: 1.01rem;
            height: 1rem;
            text-align: center;
        }

        .calendarDate {
            width: 1.01rem;
            height: 0.5rem;
            font-size: 0.4rem;
            color: #ccc;
            line-height: 0.65rem;
            text-align: center;
        }

        .calendarThing {
            width: 1rem;
            font-size: 0.22rem;
            height: 0.5rem;
            text-align: center;
            color: #ccc;
        }

        .calendar-item.disabled {
            visibility: hidden;
        }

        .calendar-item.checked {
            color: red;
        }

        .chooseDay {
            border-radius: 50%;
            background-color: #018bf0;
        }

            .chooseDay div {
                color: #fff !important;
            }

        .haoyuan-red {
            color: #D0021B;
        }

        .haoyuan-gray {
            color: #ccc;
        }

        .haoyuan-adequate {
            color: #ff9800;
        }

            .haoyuan-green {
                color: #1BA05F;
            }
        /* 时间段单击样式 */
        .btnTime{
            width: 100%;
            height: .8rem;
            font-size: .36rem;
            display: flex;
            justify-content: space-around;
            align-items: center;
            margin-top: 2vh;
        }
        .timeOld{
            width: 40%;
            height: 100%;
            border-radius: 5%;
            background: white;
            color: #4A4A4A;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .timeNow{
            width: 40%;
            height: 100%;
            border-radius: 5%;
            background: #acb2b9;
            color: white;
            display: flex;
            justify-content: center;
            align-items: center;
        }
    .TimeSpan{
        width: 100%;
        height: 100%;
        /* text-align: center; */
        display: flex;
        justify-content: center;
        align-items: center;
        background: #018bf0;
        border-radius: 5%;
        color: aliceblue;
    }


.TimeSpanTwo{
        width: 100%;
        height: 100%;
        /* text-align: center; */
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 5%;
}
        /* 跳转按钮 */
        .confirm {
            width: 92%;
            height: .96rem;
            background: #6A9BE4;
            border-radius: 5px;
            position: absolute;
            bottom: .36rem;
            left: 4%;
            font-size: .32rem;
            color: #FFFFFF;
            letter-spacing: -0.02px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        /* 消息提示 */
        .text-tip {
            display: block;
            background: rgba(0, 0, 0, .7);
            color: #fff;
            padding: 15px 15px;
            line-height: 18px;
            position: fixed;
            left: 50%;
            bottom: 55%;
            -webkit-transform: translate(-50%);
            transform: translate(-50%);
            border-radius: 3px;
            display: none;
            z-index: 9999;
            font-size: 14px;
            text-align: center;
        }
</style>

