<template>
  <div>
    <div id="details">
      <!-- 入职套餐 -->
      <div class="detailsDiv">
        <div class="detailsTop">
          <div>
            <div class="TopImg" v-if="dataList.sex == '1'">
              <img src="../../assets/detailsMan.png" alt />
            </div>
            <div class="TopImg" v-else-if="dataList.sex == '2'">
              <img src="../../assets/detailsWoman.png" alt />
            </div>
            <div class="TopImg" v-else>
              <img src="../../assets/detailsSex01.png" alt />
            </div>
          </div>
          <!-- <div v-else>         
                    <div class="TopImg">
                        <img src="../../assets/detailsSex01.png" alt="">
                    </div>               
          </div>-->
          <div class="TopTitle">
            <span>{{ dataList.clus_Name }}</span>
          </div>
          <div class="TopBtn">
            <div>
              <span>基础套餐</span>
            </div>
          </div>
        </div>
      </div>

      <!-- tab -->
      <div class="detailsDiv TabDiv">
        <div class="detailsTab">
          <div class="TabBtn">
            <div :class="[BtnState ? 'BtnBlueL' : 'BtnWhiteL']" @click="btnIntroduction">套餐简介</div>
            <div :class="[!BtnState ? 'BtnBlueR' : 'BtnWhiteR']" @click="btnKnow">体检须知</div>
          </div>
          <div class="TabText">
            <div v-if="TabText" class="TabSpan">
              <span>{{ PersonalSpan }}</span>
            </div>
            <div v-else>
              <div class="NoticeDiv" v-for="(item, index) in NoticeDiv" :key="index">
                <div class="NoticeNumber">
                  <span>{{ index + 1 }}.</span>
                </div>
                <div class="NoticeText">
                  <span>{{ item.NoticeText }}</span>
                </div>
              </div>
            </div>
          </div>
          <div class="bottomDiv"></div>
        </div>
      </div>

      <!-- 套餐项目 -->
      <div class="SetMeal">
        <div class="detailsDiv SetMealBottom">
          <div class="SetMealA">
            <div class="SetMealImg">
              <img src="../../assets/SetMeal.png" alt="套餐项目" />
            </div>
            <span>套餐项目&nbsp;&nbsp;({{ detailed.length }}项)</span>
          </div>
        </div>
        <div class="detailsDiv">
          <div class="detailed" v-for="(items, indexS) in detailed" :key="indexS">
            <div class="detailedTitle">
              <span>{{ items.comb_Name }}</span>
            </div>
            <div class="detailedText">
              <span>{{ items.note }}</span>
            </div>
            <div class="detailedBottom"></div>
          </div>
        </div>
      </div>

      <div class="footFixed">
        <div class="footLeft">
          <div class="LeftBtn">
            <span>￥</span>
            <span>{{ dataList.price }}</span>
          </div>
        </div>
        <div class="footMiddle">
          <span>您需自费的项目有{{ detailed.length }}个</span>
        </div>
        <div class="footRight" @click="ToCalendar">
          <span>下一步</span>
        </div>
      </div>
      <div class="footDiv"></div>
    </div>
  </div>
</template>
<script>
import { storage, ajax } from "../../common";
import apiUrls from "../../config/apiUrls";
import { Dialog, Toast } from "vant";
import Vue from "vue";
export default {
  data() {
    return {
      dataList: [],
      PersonalSpan: "",
      img: "",
      // Tab按钮样式
      BtnState: true,
      // Tab页文本显示
      TabText: true,
      // 体检须知文本
      NoticeDiv: [
        {
          NoticeText:
            "提前预约，号源以网站公示的号源情况为准，请尽早预订。"
        },
        {
          NoticeText:
            "如对体检项目有特殊要求，请于体检日在我中心前台进行具体咨询与调整。"
        },
        {
          NoticeText: "以上所有解释权归本健康体检中心所有！"
        }
      ],
      // 项目套餐
      detailed: [],
      // 套餐长度
      SetMealLength: 11,
      sex: "",
      type: "",
      clusIncludeItem: "",
      //判断此版本是否需要加项功能
      IsAddClusItem: Vue.prototype.baseData.IsAddClusItem || false
    };
  },
  created() {
    storage.session.delete("chooseItemPerson");
    this.dataList = JSON.parse(storage.session.get("dataList"));
    // console.log("this.dataList", this.dataList);
    this.type = storage.session.get("type");
    // this.sex=this.dataList.sex;
    this.PersonalSpan = this.dataList.PersonalSpan;
    this.GetItemCombList(this.dataList.clus_Code);
  },
  methods: {
    //获取套餐项目
    GetItemCombList(clus_Code) {
      var pData = {
        comb_code: clus_Code
      };
      ajax
        .post(apiUrls.GetItemCombList, pData, { nocrypt: true })
        .then(r => {
          var data = r.data.returnData;
          if (r.data.success) {
            this.detailed = data;
            this.clusIncludeItem = JSON.stringify(data);
            // storage.session.set("clusIncludeItem", JSON.stringify(data));
          } else {
            alert("暂无项目数据");
          }
        })
        .catch(e => {
          alert("系统繁忙！请稍后再试");
        });
    },
    // 套餐简介
    btnIntroduction() {
      this.BtnState = !this.BtnState;
      this.TabText = true;
    },
    // 体检须知
    btnKnow() {
      this.BtnState = !this.BtnState;
      this.TabText = false;
    },

    ToCalendar() {
      storage.session.set("clusIncludeItem", this.clusIncludeItem);
  
      Dialog.confirm({
        title: '提示',
        message: "如有需求，可添加套餐外的体检项目。是否添加体检项目？",
        confirmButtonText: '是',
        cancelButtonText: '否'
      })
        .then(() => {
          this.$router.push({
            path: '/addClusItem'
          });
          return;
        })
        .catch(() => {
          this.$router.push({
            path: '/PersonBookSum'
          })
          return;
        });
      // if (!this.IsAddClusItem) {
      //   storage.session.set(
      //     "chooseItem",
      //     JSON.stringify({ totalPrice: this.dataList.price })
      //   );
      //   this.$router.push({
      //     path: "/PersonBookSum"
      //   });
      //   return;
      // }
      // this.$router.push({
      //   path: "/addClusItem"
      // });
    }
  }
};
</script>
<style lang="scss">
.detailsDiv {
  width: 100%;
  background: white;
  box-sizing: border-box;
}

.detailsDiv .detailsTop {
  width: 92%;
  height: 1.24rem;
  margin: 0 auto;
  font-size: 0.28rem;
  display: flex;
  justify-content: center;
  align-items: center;
}

.detailsTop .TopImg {
  width: 1rem;
  height: 1rem;
  display: flex;
  justify-content: center;
  align-items: center;
}

.detailsTop .TopTitle {
  width: calc(100% - 2.16rem);
  height: 100%;
  font-size: 0.36rem;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.TopTitle span {
  margin-left: 0.15rem;
}

.detailsTop .TopBtn {
  width: 1.4rem;
  height: 100%;
  color: #ffffff;
  letter-spacing: -0.04rem;
  display: flex;
  align-items: center;
}

.TopBtn div {
  width: 1.4rem;
  height: 0.48rem;
  background: #6a9be4;
  border-radius: 0.04rem;
  text-align: center;
  line-height: 0.48rem;
}

/* Tab */
.TabDiv {
  border-top: 0.04rem solid #dfe3e9;
}

.detailsDiv .detailsTab {
  width: 92%;
  margin: 0 auto;
}

.detailsTab .TabBtn {
  width: 100%;
  height: 0.64rem;
  font-size: 0.28rem;
  display: flex;
  margin-top: 0.24rem;
}

.TabBtn .BtnBlueL {
  width: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #6a9be4;
  color: white;
  border-radius: 2px 0 0 2px;
}

.TabBtn .BtnWhiteL {
  width: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: white;
  color: #6a9be4;
  border: 1px solid #6a9be4;
  border-radius: 2px 0 0 2px;
}

.TabBtn .BtnBlueR {
  width: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #6a9be4;
  color: white;
  border-radius: 0 2px 2px 0;
}

.TabBtn .BtnWhiteR {
  width: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: white;
  color: #6a9be4;
  border: 1px solid #6a9be4;
  border-radius: 0 2px 2px 0;
}

.detailsTab .TabText {
  width: 100%;
  min-height: 1rem;
  font-size: 0.28rem;
  color: #9b9b9b;
  letter-spacing: -0.01px;
  line-height: 0.44rem;
  margin-top: 0.2rem;
}

.detailsTab .bottomDiv {
  width: 100%;
  height: 0.3rem;
}

.TabSpan {
  margin-top: 0.08rem;
}

.TabText .NoticeDiv {
  width: 100%;
  margin-top: 0.08rem;
  display: flex;
}

.NoticeDiv .NoticeNumber {
  width: 0.28rem;
}

.NoticeDiv .NoticeText {
  width: calc(100% - 0.28rem);
}

#details .SetMeal {
  width: 100%;
  font-size: 0.28rem;
  color: #4a4a4a;
  box-sizing: border-box;
  letter-spacing: -0.01px;
  margin-top: 0.16rem;
}

.SetMeal .SetMealBottom {
  border-bottom: 1px solid #dfe3e9;
}

.SetMeal .SetMealA {
  width: 92%;
  height: 0.84rem;
  margin: 0 auto;
  display: flex;
  align-items: center;
}

.SetMealA .SetMealImg {
  width: 0.4rem;
  height: 0.4rem;
  display: flex;
  justify-content: center;
  align-items: center;
}

.SetMealImg img {
  margin-top: 3px;
}

.SetMealA span {
  margin-left: 0.1rem;
}

.SetMeal .detailed {
  width: 92%;
  margin: 0 auto;
  border-bottom: 1px solid #dfe3e9;
}

.detailed .detailedTitle {
  width: 100%;
  height: 0.8rem;
  display: flex;
  align-items: center;
}

.detailed .detailedText {
  color: #9b9b9b;
  letter-spacing: -0.01px;
  line-height: 0.44rem;
}

.detailed .detailedBottom {
  width: 100%;
  height: 0.24rem;
}

#details .footFixed {
  width: 100%;
  height: 1.16rem;
  position: fixed;
  left: 0;
  bottom: 0;
  display: flex;
  font-size: 0.24rem;
  background: white;
  border-top: 1px solid #dfe3e9;
}

.footFixed .footLeft {
  width: 2.2rem;
  height: 96%;
  margin-left: 4%;
  color: #d0021b;
  letter-spacing: -0.02px;
  display: flex;
  align-items: center;
}

.footLeft .LeftBtn {
  width: 100%;
  height: 0.56rem;
  border-right: 2px solid #9b9b9b;
}

.LeftBtn span:nth-child(2) {
  font-size: 0.4rem;
}

.footFixed .footMiddle {
  width: 3.08rem;
  height: 100%;
  color: #9b9b9b;
  letter-spacing: -0.02px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.footFixed .footRight {
  width: 2.22rem;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #6a9be4;
  font-size: 0.32rem;
  color: #ffffff;
  letter-spacing: -0.02px;
}

#details .footDiv {
  width: 100%;
  height: 1.16rem;
}
</style>