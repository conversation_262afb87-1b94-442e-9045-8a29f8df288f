<template>
    <div>
        <div id="Personal">
            <div class="provp" v-show="provp">
                <img src="../../assets/clusGif.gif" />
            </div>
            <div class="switchBtn">
                <div :class="[OnBtn ? 'BtnBlueL' : 'BtnWhiteL']" @click="switchBtnL">男性套餐</div>
                <div :class="[!OnBtn ? 'BtnBlueR' : 'BtnWhiteR']" @click='switchBtnR'>女性套餐</div>
            </div>

            <!--模糊查询-->
            <van-search class="QueryClus" v-model="clus_Name" placeholder="请输入搜索关键词" @input="onSearch()"
                input-align="center" />

            <div class="PersonalCard top" v-for="(item, index) in CardDataList" :key="index" @click="toDetailsTips(item)">
                <div class="CardTitle">
                    <div class="titleImg" v-if="sex == '1'">
                        <img src="../../assets/detailsMan01.png" alt="套餐详情">
                    </div>
                    <div class="titleImg" v-if="sex == '2'">
                        <img src="../../assets/detailsWoman01.png" alt="套餐详情">
                    </div>
                    <div class="CardText"><span>{{ item.clus_Name }}</span></div>

                    <div class="CardSee">
                        <span>查看详情</span>
                        <span><img src="../../assets/Nextpage.png" alt="查看详情"></span>
                        <!-- <div class="CardSeeImg"><img src="./picture/Nextpage.png" alt="查看详情"></div> -->
                    </div>
                </div>
                <div class="PersonalSpan">
                    <span>{{ item.clus_Note }}</span>
                </div>
                <div class="price">
                    <span>￥</span>
                    <span>{{ item.price }}</span>
                </div>
            </div>
            <div class="footDiv"></div>
            <div v-show="clusFlag" class="clusFlag">
                <div><img src="../../assets/kong.png" /></div>
                <span style="margin-top: 10px;color: dimgray;">暂无体检套餐，请联系管理员</span>
            </div>

            <!--提示-->
            <div style="background-color: aqua;">
                <van-dialog v-model="showHun" title="温馨提示" show-cancel-button confirmButtonText="继续"
                    :beforeClose="beforeClose">
                    <div style="display: flex;justify-content: center;">
                        <div style="margin: 0.2rem;">
                            这是已婚套餐，确认已婚可选择
                        </div>
                    </div>

                </van-dialog>
            </div>

        </div>
    </div>
</template>
<script>
import { ajax, storage } from "../../common"
import apiUrls from '../../config/apiUrls';
export default {
    data() {
        return {
            OnBtn: true,//切换class
            sex: 1, // 1 男 2女  3不限性别
            CardDataList: [],//筛选后数据
            CardData: [],
            manCluster: [],
            womanCluster: [],
            clusFlag: false,
            provp: true,
            type: '',
            iimgs: require('../../assets/detailsWoman01.png'),
            clus_Name: '',//模糊查询 套餐名称
            showHun: false,
            itemClus: {}
        }
    },
    created() {
        this.type = storage.session.get('type');
        this.getClusterDetails();
    },
    methods: {
        //模糊查询套餐名称
        onSearch() {
            var that = this;
            //男性套餐过滤
            if (that.sex == 1) {
                that.CardDataList = that.manCluster.filter((val) => {
                    return val.clus_Name.includes(that.clus_Name);
                });
            } else {
                that.CardDataList = that.womanCluster.filter((val) => {
                    return val.clus_Name.includes(that.clus_Name);
                });
            }
        },
        getClusterDetails() {
            var that = this;
            var pData = {
                clus_type: "4",
            }
            ajax.post(apiUrls.GetClusItemList, pData, { nocrypt: true }).then(r => {
                var data = r.data.returnData;
                that.manCluster = data.m;
                that.womanCluster = data.w;
                that.CardDataList = that.manCluster;
                if (that.CardDataList == null || that.CardDataList.length === 0) {
                    this.clusFlag = true;
                }
                that.provp = false;
            })
        },
        switchBtnL() {
            this.clus_Name = "";
            this.OnBtn = true;
            this.sex = 1;
            this.CardDataList = this.manCluster;
            if (this.CardDataList == null || this.CardDataList.length === 0) {
                this.clusFlag = true;
            }
            else {
                this.clusFlag = false;
            }
        },
        switchBtnR() {
            this.clus_Name = "";
            this.OnBtn = false;
            this.sex = 2;
            this.CardDataList = this.womanCluster;
            if (this.CardDataList == null || this.CardDataList.length === 0) {
                this.clusFlag = true;
            }
            else {
                this.clusFlag = false;
            }
        },
        toDetailsTips(item) {
            this.itemClus = item;
            if (item.marr_status) {
                let h = storage.session.get("typeHunyun");
                if (h == "1" && item.marr_status == "2") {
                    this.showHun = true;
                    return;
                }
            }
            this.toDetails(item);
        },
        beforeClose(action, done) {
            if (action === 'confirm') {
                this.toDetails(this.itemClus);
            }
            done()
            return;
        },
        toDetails(item) {
            var dataList = {
                clus_Code: item.clus_Code,
                sex: this.sex,
                clus_Name: item.clus_Name,
                price: item.price,
                PersonalSpan: item.clus_Note
            }
            window.sessionStorage.setItem('dataList', JSON.stringify(dataList));
            this.$router.push({
                path: '/PersonDetails'
            })

        },
    }
}
</script>
<style lang="scss" scoped>
.QueryClus {
    width: 92%;
    margin: .24rem auto;
    height: .8rem;
}

.bt_common {
    width: 100%;
    /* text-align: center; */
    justify-content: center;
    align-items: center;
    display: flex;
    background-color: #3e89f9;
    color: #f7f7f7;
    font-stretch: 46px;
    letter-spacing: 4px;
}

.switchBtn {
    width: 92%;
    height: .8rem;
    font-size: .3rem;
    display: flex;
    margin: .24rem auto;
    background: white;
    font-weight: bold;
}

.switchBtn .BtnBlueL {
    width: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #6A9BE4;
    color: white;
    border-radius: 2px 0 0 2px;
}

.switchBtn .BtnWhiteL {
    width: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    background: white;
    color: #6A9BE4;
    border: 1px solid #6A9BE4;
    border-radius: 2px 0 0 2px;
}

.switchBtn .BtnBlueR {
    width: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #ff7e98;
    color: white;
    border-radius: 0 2px 2px 0;
}

.switchBtn .BtnWhiteR {
    width: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    background: white;
    color: #ff7e98;
    border: 1px solid #ff7e98;
    border-radius: 0 2px 2px 0;
}

.PersonalCard {
    width: 94%;
    height: 2.72rem;
    margin: .08rem auto;
    background: white;
    font-size: .28rem;
    border-radius: .1rem;
    -moz-box-shadow: 2px 2px 5px #333333;
    -webkit-box-shadow: 2px 2px 5px #333333;
    box-shadow: 2px 2px 5px #333333;
}

.top {
    margin-top: .24rem;
}

.PersonalCard .CardTitle {
    width: 96%;
    height: 1rem;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 4%;
}

.CardTitle .titleImg {
    width: .56rem;
    height: .56rem;
    display: flex;
    justify-content: center;
    align-items: center;
}

.CardText {
    width: calc(100% - 2.52rem);
    height: 1rem;
    display: flex;
    align-items: center;
    /* margin-left: .2rem; */
    font-size: .32rem;
    color: #4A4A4A;
    letter-spacing: -0.02px;
    font-weight: 600;
}

.CardText span {
    margin-left: .2rem;
}

.CardSee {
    width: 1.96rem;
    height: 1rem;
    color: #6A9BE4;
    letter-spacing: -0.01px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.CardSeeImg {
    width: .64rem;
    height: .64rem;
}

.icon-Nextpage {
    /*margin-top: 3px;*/
    font-size: .5rem;
}

.PersonalCard .PersonalSpan {
    width: 92%;
    height: .88rem;
    color: #9B9B9B;
    letter-spacing: -0.01px;
    line-height: .44rem;
    margin: 0 auto;
    /* 超过两行省略号 */
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.PersonalCard .price {
    width: 92%;
    margin: .08rem auto;
    font-size: .36rem;
    color: #6A9BE4;
    letter-spacing: -0.02px;
    display: flex;
    align-items: center;
}

.price span:nth-child(1) {
    font-size: .24rem;
}

#Personal .footDiv {
    width: 100%;
    height: .5rem;
}

.provp img {
    width: 100%;
    position: fixed;
    z-index: 2006;
}

.provp {
    position: fixed;
    top: 0;
    left: 0;
    background: #fff;
    width: 100vw;
    height: 100vh;
    z-index: 2005;
    opacity: 0.75;
    display: flex;
    justify-content: center;
    align-items: center;
}

.clusFlag {
    display: flex;
    flex-direction: column;
    align-items: center;
    font-size: 0.3rem;
}
</style>