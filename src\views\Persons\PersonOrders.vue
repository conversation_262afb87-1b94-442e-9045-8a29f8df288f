<template>
  <div>
    <div id="PersonalOrder">
      <!-- top -->
      <div class="Information">
        <div class="InformationBox">
          <div class="InformationDiv">
            <div class="InformationTitle">
              <b>体检人信息</b>
            </div>
            <div class="InformationInput"></div>
          </div>
          <div class="InformationDiv">
            <div class="InformationTitle">
              <span>姓名</span>
            </div>
            <div class="InformationInput">
              <input type="text" placeholder="请输入与身份证一致的姓名" v-model="InputNames" />
            </div>
          </div>
          <div class="InformationDiv">
            <div class="InformationTitle">
              <span>证件号</span>
            </div>
            <div class="InformationInput">
              <input type="text" placeholder="请输入身份证号" v-model="InputCards" maxlength="18" />
            </div>
          </div>
         
          <div class="InformationDiv">
            <div class="InformationTitle">
              <span>手机号</span>
            </div>
            <div class="InformationInput">
              <input type="text" placeholder="请输入手机号" v-model="InputTels" maxlength="11" />
            </div>
          </div>

          <div class="InformationDiv" v-if="marr_status">
            <div class="InformationTitle">
              <span>婚姻状况</span>
            </div>
            <div class="InformationInput">
              <input type="text" placeholder="" v-model="marr_status" readonly />
            </div>
          </div>

          <div class="InformationDiv" v-if="type=='healthCertificate'"  @click="isHealthCertificateType=true">
            <div class="InformationTitle">
              <span>健康证类型</span>
            </div>

            <div class="from-right">
                  <span >{{HealthCertificateType || "点击选择健康证类型"}}</span>
              </div>

          </div>

          <div class="InformationDiv"  v-if="type=='healthCertificate'" @click="isJobType=true">
            <div class="InformationTitle">
              <span>职业类型</span>
            </div>

            <div class="from-right">
                  <span >{{JobType || "点击选择职业类型"}}</span>
              </div>

          </div>

        </div>
      </div>

      <!-- middle -->
      <div class="time">
        <div class="timeDiv">
          <div class="timeTitle">
            <span>体检日期</span>
          </div>
          <div class="timeChoice">
            <span>{{ date }}（{{ week }}）</span>
          </div>
        </div>
        <div class="timeDiv">
          <div class="timeTitle">
            <span>报到时间</span>
          </div>
          <div class="timeChoice">
            <span>{{ calendarDay.sumtimeName }}</span>
          </div>
        </div>
        <div class="timeDiv">
          <div class="timeTitle">
            <span>体检类型</span>
          </div>
          <div class="timeChoice">
            <span v-if="type == 'person'">个人体检</span>
            <span v-else-if="type == 'staff'">入职体检</span>
            <span v-else-if="type == 'vehicle'">驾驶证体检</span>
            <span v-else>智能体检</span>
          </div>
        </div>

        <div class="timeDiv">
          <div class="timeTitle">
            <span>总价格</span>
          </div>
          <div class="timeChoice" style="color: #d0021b;">
            <span>￥{{ totalPrice }}</span>
          </div>
        </div>
      </div>


      <!-- 套餐 -->
      <div class="SetMeal" v-if="clusIn.length > 0">
        <van-collapse v-model="clusShowA">
          <van-collapse-item name="1">
            <template #title>
              <div class="SetTitle">
                <img src="../../assets/SetMeal.png" alt="套餐项目" />
                <div v-if="clus_name">{{ clus_name }}（共{{ clusIn.length }}项）
                  <!-- <span class="SetTitle-price">￥{{ dataList.price }}</span> -->
                </div>
                <!-- <div v-else>套餐项目&nbsp;&nbsp;（共{{ clusIn.length }}项）<span class="SetTitle-price"></span></div> -->
              </div>
            </template>
            <div class="set-content">
              <div class="content-item" v-for="(item, index) in clusIn" :key="index">
                <span>{{ index + 1 }}、{{ item.comb_Name }}</span>
                <!-- <span class="item-price">￥{{ item.price }}</span> -->
              </div>
            </div>
          </van-collapse-item>
        </van-collapse>
      </div>

      <!-- 自选项目 -->
      <div class="SetMeal" v-if="clusOut.length > 0">
        <van-collapse v-model="clusShowB">
          <van-collapse-item name="1">
            <template #title>
              <div class="SetTitle">
                <img src="../../assets/SetMeal.png" alt="自选项目" />
                <div>套餐外项目（共{{ clusOut.length }}项）
                  <!-- <span class="SetTitle-price">￥{{ combPrice }}</span> -->
                </div>
              </div>
            </template>
            <div class="set-content">
              <div class="content-item" v-for="(item, index) in clusOut" :key="index">
                <!-- 有项目价格 -->
                <!-- <span>{{ index + 1 }}、{{ item.text }}</span> -->
                <!-- <span class="item-price">￥{{ item.price }}</span> -->
                <!-- 无价格 -->
                <span>{{ index + 1 }}、{{ item.comb_name }}</span>
              </div>
            </div>
          </van-collapse-item>
        </van-collapse>
      </div>
      <!-- <div class="tips">
        <span>请注意：</span>
        <li>总价格已包含耗材费、折扣等；</li>
      </div> -->
      <div style="padding-bottom: 1.5rem;"></div>

      <!-- foot -->
      <div class="confirm" @click="ToSuccess">
        <span>确认</span>
      </div>

   
        <van-picker
  title="健康证类型"
  show-toolbar
  v-if="isHealthCertificateType"
  :columns="columns"
  @confirm="onConfirm"
  @cancel="onCancel"
  @change="onChange"
  class="HealthCertificateType"
/>

<van-picker
  title="职业型"
  show-toolbar
  v-if="isJobType"
  :columns="JobTypeList"
  @confirm="onConfirmByJob"
  @cancel="onCancelByJob"
  @change="onChangeByJob"
  class="HealthCertificateType"
/>


     


      <!--遮罩层-->
      <van-overlay :show="show" v-show="show">
        <div class="vanoverBtn">
          <van-loading type="spinner" color="#1989fa">订单生成中...</van-loading>
        </div>
      </van-overlay>
    </div>
  </div>
</template>
<script>

import { ajax, dataUtils, storage  } from "../../common";
import {HealthCertificateTypeList} from "../../common/data.js"
import apiUrls from "../../config/apiUrls";
import { Toast} from "vant";
import Vue from "vue";

export default {
  data() {
    return {
      isHealthCertificateType:false,
      isJobType:false,
      HealthCertificateType:"",
      JobType:"",
      JobTypeList:[],
      hospInfo:{},
      calendarDay: [],
      dataList: [],
      price: 0,//套餐价格
      combPrice: 0,//自选项目总价
      totalPrice: 0,//总价
      CardText: "",
      img: "",
      PersonalSpan: "",
      InputNames: "",
      InputCards: "",
      InputTels: "",
      marr_status: "",
      // option1:[{
      //   text: '未婚', value: '1'
      // },{
      //   text: '已婚', value: '2'
      // },{
      //   text: '离异', value: '3'
      // }],
      type: "",
      show: false,
      showType: true,
      chooseItem: {
        total: 0.0,
        chooseCombCode: ""
      }, //加减项选择的项目和总价对象
      clusIn: [],//套餐内项目
      clusOut: [],//套餐外项目
      clus_name: "",//套餐名
      clusShowA: [],
      clusShowB: [],
      otherCombs: [],//体检系统已加项的
      clusShowC: [],
      //判断此版本是否需要加项功能
      IsAddClusItem: false,
      columns: ['食品', '非食品', '服务', '其他'],
      HealthList:[{
        code:'T',
        value:'食品'
      },{
        code:'P',
        value:'非食品'
      },{
        code:'S',
        value:'服务'
      },{
        code:'O',
        value:'其他'
      }]
     // T=食品，P=非食品，S=服务，O=其他
    };
  },
  created() {
  console.log( HealthCertificateTypeList.map(h=>h.job_name))
  this.JobTypeList=HealthCertificateTypeList.map(h=>h.job_name)
    this.hospInfo = JSON.parse(storage.session.get("hospInfo"));
    let user = JSON.parse(storage.cookie.get("user"));
    this.InputNames = user.name;
    this.InputCards = user.idCard;
    this.InputTels = user.tel;
    this.type = storage.session.get("type");
    this.dataList = JSON.parse(storage.session.get("dataList"));
    // console.log(" this.dataList", this.dataList);
    this.clus_name = this.dataList.clus_Name;
    this.price = this.dataList.price;//套餐价格
    this.totalPrice = this.price;//总价格

    this.clusIn = JSON.parse(storage.session.get("clusIncludeItem"));
    this.calendarDay = JSON.parse(storage.session.get("calendarDay"));
    let chooseItem = JSON.parse(storage.session.get("chooseItemPerson")); //获取加减项选择的项目和总价对象
    if (chooseItem) {
      this.combPrice = chooseItem.totalPrice;
      // console.log("this.totalPrice",this.totalPrice,this.combPrice);
      // this.totalPrice = (this.totalPrice * 100 + this.combPrice * 100) / 100
      this.totalPrice=dataUtils.priceSum(this.totalPrice,this.combPrice)
      this.clusOut = chooseItem.clusOut
    }
    this.date = this.calendarDay.date;
    var weekDay = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
    this.week = weekDay[this.calendarDay.week];
    let marr = storage.session.get("typeHunyun");
    if (marr) {
      if (marr == "1") {
        this.marr_status = "未婚"
      }
      else if (marr == "2") {
        this.marr_status = "已婚"
      }
    }

  },
  methods: {
    onConfirmByJob(value, index) {
      Toast(`当前值：${value}`);
      this.JobType=value;
      this.isJobType=false
    },
    onChangeByJob(picker, value, index) {
      Toast(`当前值：${value}`);
      // this.isHealthCertificateType=false
    },
    onCancelByJob() {
      Toast('取消');
      this.isJobType=false
    },
    onConfirm(value, index) {
      Toast(`当前值：${value}`);
      this.HealthCertificateType=value;
      this.isHealthCertificateType=false
    },
    onChange(picker, value, index) {
      Toast(`当前值：${value}`);
      // this.isHealthCertificateType=false
    },
    onCancel() {
      Toast('取消');
      this.isHealthCertificateType=false
    },
    ToSuccess() {
      if (this.InputNames == "") {
        Toast("请输入姓名");
        return;
      }
      if (this.InputCards == "") {
        Toast("请输入身份证件号码");
        return;
      }
      if (this.InputTels == "") {
        Toast("请输入手机号码");
        return;
      }

      if(this.type=='healthCertificate'){
        if (this.HealthCertificateType == "") {
        Toast("请输入健康证类型");
        return;
      }
      if (this.JobType == "") {
        Toast("请输入职业");
        return;
      }
      }
      
      
      
      if (dataUtils.isTel(this.InputTels) != true) {
        Toast(dataUtils.isTel(this.InputTels));
        return;
      }
      if (dataUtils.isCardID(this.InputCards) != true) {
        Toast(dataUtils.isCardID(this.InputCards));
        return;
      }
      if (this.dataList.sex != 3) {
        var Sex = parseInt(this.InputCards.substr(16, 1)) % 2;
        if (Sex == 1) {
          if (this.dataList.sex != 1) {
            Toast("请选择对应性别的套餐！");
            return;
          }
        }
        if (Sex == 0) {
          if (this.dataList.sex != 2) {
            Toast("请选择对应性别的套餐！");
            return;
          }
        }
      }
   
       let  outItem=this.clusOut.map(r => r.comb_code.trim()).join(',')

      
      

      
      let pData = {
        openid: storage.cookie.get("openid"),
        name: this.InputNames,
        idCard: this.InputCards,
        tel: this.InputTels,
        clus_Name: this.dataList.clus_Name,
        clus_Code: this.dataList.clus_Code,
        begin_Time: this.calendarDay.date,
        type: this.type,
        sumtime_Code: this.calendarDay.sumtime_Code,
        sumtime_Name: this.calendarDay.sumtimeName,
        price: this.totalPrice,//总价
        choose_comb_code: outItem,//加项
        hflag:this.type=='healthCertificate'?this.HealthList.find(x=>x.value==this.HealthCertificateType).code:"",
        job:this.type=='healthCertificate'?HealthCertificateTypeList.find(x=>x.job_name==this.JobType).job_code:"",
        hospCode:this.hospInfo.hospCode,
        marr_status:storage.session.get("typeHunyun"),
        questionZC:storage.session.get("questionInfoZC")
      };

      console.log(pData)




      this.show = true;
      ajax
        .post(apiUrls.PersonOrderAdd, pData, { nocrypt: true })
        .then(r => {
          if (r.data.success) {
            storage.session.set(
              "OrderList",
              JSON.stringify(r.data.returnData.ord)
            );
            //更新cookie信息
            storage.cookie.set("user", JSON.stringify(r.data.returnData.uid));
            Toast(r.data.returnMsg);
            setTimeout(() => {
              this.$router.replace({
                path: "/PersonPayment"
              });
              this.show = false;
            }, 2000);
          } else {
            this.show = false;
            Toast(r.data.returnMsg);
            return;
          }
        })
        .catch(e => {
          alert("系统繁忙！请稍后再试");
        });
    },
  }
};
</script>
<style lang="scss" scoped>
#PersonalOrder .Information {
  width: 100%;
  //height: 3.48rem;
  background: white;
  position: relative;
  top: 0;
  left: 0;
  box-sizing: border-box;
}

.Information .InformationBox {
  width: 92%;
  height: 100%;
  margin: 0 auto;
  /* border: 1px solid; */
}

.InformationBox .InformationDiv {
  width: 100%;
  height: 0.87rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #dfe3e9;
  box-sizing: border-box;
  font-size: 0.28rem;
}

.InformationDiv .InformationTitle {
  width: 30%;
  height: 100%;
  display: flex;
  align-items: center;
  color: #4a4a4a;
}

.cluscs {
  width: 100%;
  height: 0.87rem;
  background: white;
  margin-top: 0.16rem;
}

.cluscs .timeDiv {
  width: 92%;
  height: 0.87rem;
  margin: 0 auto;
  box-sizing: border-box;
  font-size: 0.28rem;
  display: flex;
}

.cluscs .timeTitle {
  width: 30%;
  height: 100%;
  display: flex;
  align-items: center;
  color: #4a4a4a;
}

.cluscs .timeChoice {
  width: 70%;
  height: 100%;
  color: #9b9b9b;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.InformationDiv .InformationInput {
  width: 70%;
  height: 100%;
  display: flex;
  color: #9b9b9b;
  margin-right: 0.1rem;
}

.InformationInput input {
  width: 100%;
  // height: 100%;
  border: none;
  outline: medium;
  text-align: right;
}

#PersonalOrder .time {
  width: 100%;
  //height: 2.6rem;
  background: white;
  margin-top: 0.16rem;
}

.time .timeDiv {
  width: 92%;
  height: 0.87rem;
  margin: 0 auto;
  box-sizing: border-box;
  font-size: 0.28rem;
  display: flex;
  border-bottom: 1px solid #dfe3e9;
}

.timeDiv .timeTitle {
  width: 30%;
  height: 100%;
  display: flex;
  align-items: center;
  color: #4a4a4a;
}

.timeDiv .timeChoice {
  width: 70%;
  height: 100%;
  color: #9b9b9b;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.timeChoice .priceChoice {
  font-size: 0.24rem;
  color: #d0021b;
  letter-spacing: -0.02px;
}

.timeChoice span:nth-child(2) {
  font-size: 0.4rem;
  color: #d0021b;
  letter-spacing: -0.02px;
  line-height: 0.36rem;
}

/* 套餐 */
// #PersonalOrder .SetMealNow {
//   width: 100%;
//   /* height: 2.66rem; */
//   background: white;
//   margin-top: 0.16rem;
//   padding: 0.36rem 0;
// }

.SetMealNow .NowBox {
  width: 92%;
  height: 100%;
  margin: 0 auto;
  box-sizing: border-box;
  font-size: 0.28rem;
}

.NowBox .SetTitle {
  width: 100%;
  height: 1rem;
  display: flex;
}

.SetTitle .SetImg {
  width: 0.75rem;
  height: 1rem;
  display: flex;
  // justify-content: start;
  align-items: center;
}

.SetImg img {
  margin-top: 3px;
}

.SetTitle .SetText {
  font-size: 0.32rem;
  color: #4a4a4a;
  letter-spacing: -0.02px;
  font-weight: 600;
  display: flex;
  align-items: center;
}

// .collapse-item-content-padding{
//   padding-top:0;
// }
.NowBox .MealText {
  width: 100%;
  min-height: 1.3rem;
  max-height: 3.5rem;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #9b9b9b;
  letter-spacing: -0.01px;
  line-height: 0.44rem;
  overflow-y: scroll;
}

#OrderDetails .SetMeal {
  width: 100%;
  font-size: 0.28rem;
  color: #4a4a4a;
  box-sizing: border-box;
  letter-spacing: -0.01px;
  margin-top: 0.16rem;
}

.SetTitle {
  // width: 100%;
  // height: 1rem;
  display: flex;
  align-items: center;
}

.SetTitle img {
  margin-right: 0.1rem;
}

.SetTitle-price {
  color: #d0021b;
}

.set-content {
  margin-top: 0;
  font-size: 0.3rem;
  color: #b7b7b7;
}

.content-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  // margin-bottom: 0.12rem;
  font-size: 0.28rem;
}

.item-price {
  color: #d0021b;
}

.tips {
  width: 100%;
  font-size: 0.28rem;
  color: #D0021B;
  margin-left: 0.2rem;
  margin-top: 0.2rem;
}

/* 跳转按钮 */
.confirm {
  width: 92%;
  height: 0.96rem;
  background: #6a9be4;
  border-radius: 5px;
  position: fixed;
  bottom: 0.36rem;
  left: 4%;
  font-size: 0.32rem;
  color: #ffffff;
  letter-spacing: -0.02px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.cancelfirm {
  width: 92%;
  height: 0.96rem;
  background: #b7b7b7;
  border-radius: 5px;
  position: fixed;
  bottom: 0.36rem;
  left: 4%;
  font-size: 0.32rem;
  color: #ffffff;
  letter-spacing: -0.02px;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 消息提示 */
.text-tip {
  display: block;
  background: rgba(0, 0, 0, 0.7);
  color: #fff;
  padding: 15px 15px;
  line-height: 18px;
  position: fixed;
  left: 50%;
  bottom: 55%;
  -webkit-transform: translate(-50%);
  transform: translate(-50%);
  border-radius: 3px;
  display: none;
  z-index: 9999;
  font-size: 14px;
  text-align: center;
}

.van-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.vanoverBtn {
  display: flex;
  width: 50%;
  height: 1rem;
  background-color: #fdfdfd;
  justify-content: center;
  align-items: center;
  border-radius: 30px;
}

.HealthCertificateType{
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100vw;
  height: 50vh;

}
</style>
