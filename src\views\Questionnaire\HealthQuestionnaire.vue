<template>
  <div class="health-questionnaire-page">
    <div class="questionnaire-header">
      <div class="header-logo">
        <img :src="`${publicPath}img/HomeLogo.jpg`" />
      </div>
      <!-- <div class="header-title">
        <p>健康问卷</p>
      </div> -->
    </div>

    <div class="questionnaire-intro">
      <p>请填写以下健康问卷，以便我们为您提供更好的体检服务。</p>
    </div>

    <!-- 引入健康问卷组件 -->
    <health-questionnaire-form @on-submit="handleSubmit" />
  </div>
</template>

<script>
import apiUrls from '../../config/apiUrls';
import { ajax, storage } from "../../common";
import HealthQuestionnaireForm from '@/components/HealthQuestionnaire.vue'
import { Toast } from "vant";

export default {
  name: 'HealthQuestionnaire',
  components: {
    HealthQuestionnaireForm
  },
  data() {
    return {
      publicPath: process.env.BASE_URL,
      page: '',
      userInfo: {}
    }
  },
  created() {
    this.page = this.$route.query['page'];
  
    if (storage.session.get("questionInfoZCA")) {
      this.userInfo = JSON.parse(storage.session.get("questionInfoZCA"));
    }
  },
  methods: {

    approveItem(regNo,ordinaryQs) {
      var pData={
        regno:regNo,
        kw:'1',
        questionZC:ordinaryQs
      }
      ajax.post(apiUrls.SaveQuestions, pData, { nocrypt: true }).then(r=>{

   
      Toast(`同步成功`);
      })
      
    
    },
   
    handleSubmit(formData) {
      // 保存问卷数据
      storage.session.set("medicalQuestionnaireData", JSON.stringify(formData));

      // 构建问卷答案数据，与现有系统兼容
      const formattedData = [];

      // 添加问卷答案
      Object.entries(formData).forEach(([key, value]) => {
        let questionLabel = '';
        switch (key) {
          case 'smoking': questionLabel = '吸烟史'; break;
          case 'drinking': questionLabel = '饮酒史'; break;
          case 'hypertension': questionLabel = '高血压'; break;
          case 'heartDisease': questionLabel = '冠心病'; break;
          case 'cerebrovascular': questionLabel = '脑血管病'; break;
          case 'diabetes': questionLabel = '糖尿病'; break;
          case 'tuberculosis': questionLabel = '肺结核'; break;
          case 'gynecologic': questionLabel = '妇科病'; break;
          case 'surgery': questionLabel = '手术外伤史'; break;
          case 'tumor': questionLabel = '恶性肿瘤'; break;
          case 'other': questionLabel = '其他疾病'; break;
        }

        if (questionLabel) {
          formattedData.push({
            Q: questionLabel,
            A: value
          });
        }
      });

      // 保存格式化的数据
      storage.session.set("questionInfoZC", JSON.stringify(formattedData));

      var pData = {

        openId: storage.cookie.get("openid"),
        ordinaryQs: JSON.stringify(formData),


      };

      this.approveItem(this.$route.query.regNo, JSON.stringify(formData))

      ajax.post(apiUrls.SaveGetQuestion, pData, { nocrypt: true }).then(r => {
        let res = r.data
        console.log(res.returnData)
     
      
        res.success ? this.$toast.success('问卷提交成功') : this.$toast.fail('提交失败，请重试');

      })


      Toast.success("问卷提交成功！");
      this.$router.push({ path: "/TeamBookSum" });

      // 根据页面来源进行跳转
    
    }
  }
}
</script>

<style lang="scss" scoped>
.health-questionnaire-page {
  min-height: 100vh;
  background-color: #f7f8fa;

  .questionnaire-header {
    background-color: #fff;
    padding: 16px;
    display: flex;
    align-items: center;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);

    .header-logo {
      width: 6rem;
      height: .5rem;

      img {
        display: block;
        width: 100%;
        height: 100%;
      }
    }

    .header-title {
      p {
        margin: 0;
        font-size: 18px;
        font-weight: bold;
        color: #323233;
      }
    }
  }

  .questionnaire-intro {
    padding: 16px;
    background-color: #fff;
    margin: 12px 0;
    font-size: 14px;
    color: #666;
    line-height: 1.5;
  }
}

.van-radio {
  font-size: .25rem !important;
}
</style>