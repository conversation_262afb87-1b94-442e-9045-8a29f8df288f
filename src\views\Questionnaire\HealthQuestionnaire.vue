<template>
  <div class="health-questionnaire-page">
    <div class="questionnaire-header">
      <div class="header-logo">
        <img :src="`${publicPath}img/HomeLogo.jpg`" />
      </div>
      <!-- <div class="header-title">
        <p>健康问卷</p>
      </div> -->
    </div>

    <div class="questionnaire-intro">
      <p>请填写以下健康问卷，以便我们为您提供更好的体检服务。</p>
    </div>

    <!-- 引入健康问卷组件 -->
    <health-questionnaire-form @on-submit="handleSubmit" />
  </div>
</template>

<script>
import apiUrls from '../../config/apiUrls';
import { ajax, storage } from "../../common";
import HealthQuestionnaireForm from '@/components/HealthQuestionnaire.vue'
import { Toast } from "vant";

export default {
  name: 'HealthQuestionnaire',
  components: {
    HealthQuestionnaireForm
  },
  data() {
    return {
      publicPath: process.env.BASE_URL,
      page: '',
      userInfo: {}
    }
  },
  created() {
    this.page = this.$route.query['page'];

    if (storage.session.get("questionInfoZCA")) {
      this.userInfo = JSON.parse(storage.session.get("questionInfoZCA"));
    }
  },
  methods: {

    approveItem(regNo,ordinaryQs) {
      var pData={
        regno:regNo,
        kw:'1',
        questionZC:ordinaryQs
      }
      ajax.post(apiUrls.SaveQuestions, pData, { nocrypt: true }).then(r=>{


      Toast(`同步成功`);
      })


    },

    handleSubmit(formData) {
      // 保存问卷数据
      storage.session.set("medicalQuestionnaireData", JSON.stringify(formData));

      // 构建问卷答案数据，与现有系统兼容
      const formattedData = [];

      // 添加基本信息
      formattedData.push({ Q: '单位', A: formData.company || '-' });
      formattedData.push({ Q: '姓名', A: formData.name || '-' });
      formattedData.push({ Q: '联系方式', A: formData.contact || '-' });
      formattedData.push({ Q: '家属手机', A: formData.familyContact || '-' });

      // 添加健康状况
      formattedData.push({ Q: '健康状况', A: formData.healthStatus || '-' });
      formattedData.push({ Q: '手术史', A: formData.surgeryHistory === '有' ? `有(${formData.surgeryName})` : '无' });
      formattedData.push({ Q: '药物过敏史', A: formData.allergyHistory === '有' ? `有(${formData.allergyName})` : '无' });

      // 添加疾病史
      if (formData.diseaseHistory === '有') {
        // 处理疾病表格数据
        Object.entries(formData.diseases).forEach(([key, value]) => {
          let diseaseName = '';
          switch (key) {
            case 'diabetes': diseaseName = '糖尿病'; break;
            case 'hypertension': diseaseName = '高血压'; break;
            case 'hyperlipidemia': diseaseName = '血脂异常'; break;
            case 'heartDisease': diseaseName = '心脏病'; break;
            case 'cerebrovascular': diseaseName = '脑血管病'; break;
            case 'malignantTumor': diseaseName = '恶性肿瘤'; break;
          }

          if (value.hasDiagnosed) {
            const diseaseInfo = `已患病${value.duration}年，${value.hasMedication ? '已用药' : '未用药'}，${value.hasMedication ? (value.followPrescription === '是' ? '遵医嘱服药' : '未遵医嘱服药') : ''}`;
            formattedData.push({ Q: diseaseName, A: diseaseInfo });
          }

          if (value.familyHistory) {
            formattedData.push({ Q: `${diseaseName}家族史`, A: '有' });
          }
        });

        if (formData.otherDisease) {
          formattedData.push({ Q: '其他疾病', A: formData.otherDisease });
        }
      } else {
        formattedData.push({ Q: '疾病史', A: '无' });
      }

      // 添加生活方式信息
      if (formData.smoking === '吸烟') {
        formattedData.push({ Q: '吸烟史', A: `每日平均${formData.smokingAmount}支，已吸烟${formData.smokingYears}年` });
      } else {
        formattedData.push({ Q: '吸烟史', A: '不吸烟' });
      }

      formattedData.push({ Q: '饮酒史', A: formData.drinking || '-' });
      formattedData.push({ Q: '饮食习惯', A: formData.dietHabit || '-' });

      if (formData.tastePreference && formData.tastePreference.length > 0) {
        formattedData.push({ Q: '口味偏好', A: formData.tastePreference.join('、') });
      }

      formattedData.push({ Q: '睡眠情况', A: `夜间${formData.sleepHours}小时，午睡${formData.napMinutes}分钟，质量${formData.sleepQuality}` });
      formattedData.push({ Q: '作息情况', A: formData.workSchedule || '-' });

      // 添加运动情况
      formattedData.push({ Q: '运动频率', A: formData.exerciseFrequency || '-' });

      if (formData.exerciseTypes && formData.exerciseTypes.length > 0) {
        formattedData.push({ Q: '锻炼方式', A: formData.exerciseTypes.join('、') });
      }

      // 添加心理状况
      formattedData.push({ Q: '是否容易焦虑', A: formData.anxious || '-' });
      formattedData.push({ Q: '近期焦虑状况', A: formData.recentAnxiety || '-' });

      // 保存格式化的数据
      storage.session.set("questionInfoZC", JSON.stringify(formattedData));

      var pData = {
        openId: storage.cookie.get("openid"),
        ordinaryQs: JSON.stringify(formData),
      };

      // 如果有注册号，则调用审批接口
      if (this.$route.query.regNo) {
        this.approveItem(this.$route.query.regNo, JSON.stringify(formData));
      }

      // 保存问卷数据到服务器
      ajax.post(apiUrls.SaveGetQuestion, pData, { nocrypt: true }).then(r => {
        let res = r.data;
        if (res.success) {
          Toast.success("问卷提交成功！");

          // 根据页面来源进行跳转
          const fromPage = this.$route.query.page;
          if (fromPage) {
            // 如果有特定的来源页面，则返回该页面
            this.$router.push({ path: `/${fromPage}` });
          } else {
            // 默认跳转到团队预约汇总页面
            this.$router.push({ path: "/TeamBookSum" });
          }
        } else {
          Toast.fail('提交失败，请重试');
        }
      }).catch(error => {
        console.error('保存问卷失败', error);
        Toast.fail('提交失败，请重试');
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.health-questionnaire-page {
  min-height: 100vh;
  background-color: #f7f8fa;

  .questionnaire-header {
    background-color: #fff;
    padding: 16px;
    display: flex;
    align-items: center;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);

    .header-logo {
      width: 6rem;
      height: .5rem;

      img {
        display: block;
        width: 100%;
        height: 100%;
      }
    }

    .header-title {
      p {
        margin: 0;
        font-size: 18px;
        font-weight: bold;
        color: #323233;
      }
    }
  }

  .questionnaire-intro {
    padding: 16px;
    background-color: #fff;
    margin: 12px 0;
    font-size: 14px;
    color: #666;
    line-height: 1.5;
  }
}

.van-radio {
  font-size: .25rem !important;
}
</style>