<template>
  <div class="medical-questionnaire">
    <div class="questionnaire-header">
      <div class="header-logo">
        <img :src="`${publicPath}img/HomeLogo.jpg`" />
      </div>
      <div class="header-title">
        <p>微信问卷</p>
      </div>
    </div>

    <div class="questionnaire-content">
      <!-- 问卷说明 -->
      <div class="questionnaire-intro">
        <p>请填写以下健康问卷，以便我们为您提供更好的体检服务。</p>
      </div>

      <!-- 表格式问卷 -->
      <div class="questionnaire-table">
        <!-- 工作生活信息 -->
        <table>
          <tr>
            <td rowspan="2" class="category-cell">工作<br/>生活<br/>信息</td>
            <td class="question-cell">吸烟史：</td>
            <td class="answer-cell">
              <van-radio-group v-model="smokingHistory" direction="horizontal">
                <van-radio name="有">有</van-radio>
                <van-radio name="无">无</van-radio>
              </van-radio-group>
            </td>
            <td class="question-cell">饮酒史：</td>
            <td class="answer-cell">
              <van-radio-group v-model="drinkingHistory" direction="horizontal">
                <van-radio name="经常">经常</van-radio>
                <van-radio name="无">无</van-radio>
              </van-radio-group>
            </td>
          </tr>
        </table>

        <!-- 既往病史 -->
        <table class="mt-10">
          <tr>
            <td rowspan="4" class="category-cell">既<br/>往<br/>病<br/>史</td>
            <td class="question-cell">高血压：</td>
            <td class="answer-cell">
              <van-radio-group v-model="hypertension" direction="horizontal">
                <van-radio name="有">有</van-radio>
                <van-radio name="无">无</van-radio>
              </van-radio-group>
            </td>
            <td class="question-cell">冠心病：</td>
            <td class="answer-cell">
              <van-radio-group v-model="heartDisease" direction="horizontal">
                <van-radio name="有">有</van-radio>
                <van-radio name="无">无</van-radio>
              </van-radio-group>
            </td>
            <td class="question-cell">脑血管病：</td>
            <td class="answer-cell">
              <van-radio-group v-model="cerebrovascularDisease" direction="horizontal">
                <van-radio name="有">有</van-radio>
                <van-radio name="无">无</van-radio>
              </van-radio-group>
            </td>
          </tr>
          <tr>
            <td class="question-cell">糖尿病：</td>
            <td class="answer-cell">
              <van-radio-group v-model="diabetes" direction="horizontal">
                <van-radio name="有">有</van-radio>
                <van-radio name="无">无</van-radio>
              </van-radio-group>
            </td>
            <td class="question-cell">肺结核：</td>
            <td class="answer-cell">
              <van-radio-group v-model="tuberculosis" direction="horizontal">
                <van-radio name="有">有</van-radio>
                <van-radio name="无">无</van-radio>
              </van-radio-group>
            </td>
            <td class="question-cell">妇科病：</td>
            <td class="answer-cell">
              <van-radio-group v-model="gynecologicalDisease" direction="horizontal">
                <van-radio name="有">有</van-radio>
                <van-radio name="无">无</van-radio>
              </van-radio-group>
            </td>
          </tr>
          <tr>
            <td class="question-cell">手术外伤史：</td>
            <td class="answer-cell">
              <van-radio-group v-model="surgeryHistory" direction="horizontal">
                <van-radio name="有">有</van-radio>
                <van-radio name="无">无</van-radio>
              </van-radio-group>
            </td>
            <td class="question-cell">恶性肿瘤：</td>
            <td class="answer-cell">
              <van-radio-group v-model="malignantTumor" direction="horizontal">
                <van-radio name="有">有</van-radio>
                <van-radio name="无">无</van-radio>
              </van-radio-group>
            </td>
            <td class="question-cell">其他：</td>
            <td class="answer-cell">
              <van-radio-group v-model="otherDiseases" direction="horizontal">
                <van-radio name="有">有</van-radio>
                <van-radio name="无">无</van-radio>
              </van-radio-group>
            </td>
          </tr>
        </table>

        <!-- 家族病史 -->
        <table class="mt-10" v-if="showFamilyHistory">
          <tr>
            <td rowspan="2" class="category-cell">家<br/>族<br/>病<br/>史</td>
            <td class="question-cell">父母高血压：</td>
            <td class="answer-cell">
              <van-radio-group v-model="familyHypertension" direction="horizontal">
                <van-radio name="有">有</van-radio>
                <van-radio name="无">无</van-radio>
              </van-radio-group>
            </td>
            <td class="question-cell">父母糖尿病：</td>
            <td class="answer-cell">
              <van-radio-group v-model="familyDiabetes" direction="horizontal">
                <van-radio name="有">有</van-radio>
                <van-radio name="无">无</van-radio>
              </van-radio-group>
            </td>
            <td class="question-cell">父母心脏病：</td>
            <td class="answer-cell">
              <van-radio-group v-model="familyHeartDisease" direction="horizontal">
                <van-radio name="有">有</van-radio>
                <van-radio name="无">无</van-radio>
              </van-radio-group>
            </td>
          </tr>
        </table>
      </div>

      <!-- 操作按钮区 -->
      <div class="action-buttons">
        <!-- 切换显示家族病史 -->
        <div class="toggle-section">
          <van-switch v-model="showFamilyHistory" size="20px" />
          <span class="toggle-label">显示家族病史</span>
        </div>

        <!-- 添加更多病史按钮 -->
        <div class="add-more-container">
          <van-button icon="plus" type="primary" size="small" @click="addMoreHistory">添加备注</van-button>
        </div>
      </div>

      <!-- 备注区域 -->
      <div class="notes-section" v-if="otherMedicalNotes">
        <div class="notes-title">备注信息：</div>
        <div class="notes-content">{{ otherMedicalNotes }}</div>
      </div>

      <!-- 提交按钮 -->
      <div class="submit-container">
        <van-button type="primary" block @click="submitQuestionnaire">提交问卷</van-button>
      </div>
    </div>

    <!-- 确认提交弹窗 -->
    <van-dialog v-model="showConfirmDialog" title="确认提交" show-cancel-button @confirm="confirmSubmit">
      <div class="dialog-content">
        您确定要提交问卷吗？提交后将无法修改。
      </div>
    </van-dialog>

    <!-- 加载遮罩 -->
    <van-overlay :show="loading">
      <div class="loading-container">
        <van-loading type="spinner" color="#1989fa">提交中...</van-loading>
      </div>
    </van-overlay>
  </div>
</template>

<script>
import { storage } from "../../common";
import { Dialog, Toast } from "vant";

export default {
  name: "MedicalQuestionnaire",
  data() {
    return {
      publicPath: process.env.BASE_URL,
      // 工作生活信息
      smokingHistory: "",
      drinkingHistory: "",

      // 既往病史
      hypertension: "",
      heartDisease: "",
      cerebrovascularDisease: "",
      diabetes: "",
      tuberculosis: "",
      gynecologicalDisease: "",
      surgeryHistory: "",
      malignantTumor: "",
      otherDiseases: "",

      // 其他数据
      showConfirmDialog: false,
      loading: false,
      userInfo: {},
      page: ""
    };
  },
  created() {
    this.page = this.$route.query['page'];
    // 如果有用户信息，则获取
    if (storage.session.get("questionInfoZCA")) {
      this.userInfo = JSON.parse(storage.session.get("questionInfoZCA"));
    }

    // 如果有已保存的问卷数据，则恢复
    if (storage.session.get("medicalQuestionnaireData")) {
      const savedData = JSON.parse(storage.session.get("medicalQuestionnaireData"));
      this.smokingHistory = savedData.smokingHistory || "";
      this.drinkingHistory = savedData.drinkingHistory || "";
      this.hypertension = savedData.hypertension || "";
      this.heartDisease = savedData.heartDisease || "";
      this.cerebrovascularDisease = savedData.cerebrovascularDisease || "";
      this.diabetes = savedData.diabetes || "";
      this.tuberculosis = savedData.tuberculosis || "";
      this.gynecologicalDisease = savedData.gynecologicalDisease || "";
      this.surgeryHistory = savedData.surgeryHistory || "";
      this.malignantTumor = savedData.malignantTumor || "";
      this.otherDiseases = savedData.otherDiseases || "";
    } else {
      // 默认值设置
      this.smokingHistory = "无";
      this.drinkingHistory = "无";
      this.hypertension = "无";
      this.heartDisease = "无";
      this.cerebrovascularDisease = "无";
      this.diabetes = "无";
      this.tuberculosis = "无";
      this.gynecologicalDisease = "无";
      this.surgeryHistory = "无";
      this.malignantTumor = "无";
      this.otherDiseases = "无";
    }
  },
  methods: {
    // 添加更多病史
    addMoreHistory() {
      Dialog.confirm({
        title: '添加更多病史',
        message: '您可以在备注中添加其他病史信息，是否继续？',
        confirmButtonText: '添加',
        cancelButtonText: '取消'
      })
      .then(() => {
        Dialog.prompt({
          title: '其他病史',
          message: '请输入您的其他病史信息',
          confirmButtonText: '确认',
          inputType: 'textarea'
        }).then(value => {
          if (value) {
            Toast.success('添加成功');
            // 存储其他病史信息
            storage.session.set("otherMedicalHistory", value);
          }
        });
      });
    },

    // 提交问卷
    submitQuestionnaire() {
      // 验证必填项
      if (!this.validateForm()) {
        return;
      }

      // 显示确认对话框
      this.showConfirmDialog = true;
    },

    // 验证表单
    validateForm() {
      // 检查工作生活信息
      if (!this.smokingHistory || !this.drinkingHistory) {
        Toast("请完成工作生活信息部分");
        return false;
      }

      // 检查既往病史
      if (!this.hypertension || !this.heartDisease || !this.cerebrovascularDisease ||
          !this.diabetes || !this.tuberculosis || !this.gynecologicalDisease ||
          !this.surgeryHistory || !this.malignantTumor || !this.otherDiseases) {
        Toast("请完成既往病史部分");
        return false;
      }

      return true;
    },

    // 确认提交
    confirmSubmit() {
      this.loading = true;

      // 构建问卷数据
      const questionnaireData = {
        // 工作生活信息
        smokingHistory: this.smokingHistory,
        drinkingHistory: this.drinkingHistory,

        // 既往病史
        hypertension: this.hypertension,
        heartDisease: this.heartDisease,
        cerebrovascularDisease: this.cerebrovascularDisease,
        diabetes: this.diabetes,
        tuberculosis: this.tuberculosis,
        gynecologicalDisease: this.gynecologicalDisease,
        surgeryHistory: this.surgeryHistory,
        malignantTumor: this.malignantTumor,
        otherDiseases: this.otherDiseases
      };

      // 保存问卷数据
      storage.session.set("medicalQuestionnaireData", JSON.stringify(questionnaireData));

      // 构建问卷答案数据，与现有系统兼容
      const formattedData = [];

      // 添加工作生活信息
      formattedData.push({
        Q: "吸烟史",
        A: this.smokingHistory
      });

      formattedData.push({
        Q: "饮酒史",
        A: this.drinkingHistory
      });

      // 添加既往病史
      formattedData.push({
        Q: "高血压",
        A: this.hypertension
      });

      formattedData.push({
        Q: "冠心病",
        A: this.heartDisease
      });

      formattedData.push({
        Q: "脑血管病",
        A: this.cerebrovascularDisease
      });

      formattedData.push({
        Q: "糖尿病",
        A: this.diabetes
      });

      formattedData.push({
        Q: "肺结核",
        A: this.tuberculosis
      });

      formattedData.push({
        Q: "妇科病",
        A: this.gynecologicalDisease
      });

      formattedData.push({
        Q: "手术外伤史",
        A: this.surgeryHistory
      });

      formattedData.push({
        Q: "恶性肿瘤",
        A: this.malignantTumor
      });

      formattedData.push({
        Q: "其他病史",
        A: this.otherDiseases
      });

      // 添加其他病史备注
      if (storage.session.get("otherMedicalHistory")) {
        formattedData.push({
          Q: "其他病史备注",
          A: storage.session.get("otherMedicalHistory")
        });
      }

      // 保存格式化的数据
      storage.session.set("questionInfoZC", JSON.stringify(formattedData));

      // 模拟提交过程
      setTimeout(() => {
        this.loading = false;
        Toast.success("问卷提交成功！");

        // 根据页面来源进行跳转
        switch (this.page) {
          case "person":
            this.$router.push({
              path: "/PersonIndex",
            });
            break;
          case "vehicle":
            this.$router.push({
              path: "/VehicleIndex",
            });
            break;
          case "staff":
            this.$router.push({
              path: "/StaffIndex",
            });
            break;
          case "group":
            this.$router.push({
              path: "/TeamBookSum",
            });
            break;
          default:
            this.$router.push({
              path: "/",
            });
        }
      }, 1500);
    }
  }
};
</script>

<style lang="scss" scoped>


/* 基础样式 */
.medical-questionnaire {
  width: 100%;
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-bottom: 1.25rem;
  font-size: .25rem!important;
}

.questionnaire-header {
  display: flex;
  align-items: center;
  padding: 0.9375rem;
  background-color: #fff;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.header-logo {
  width: 2.5rem;
  height: 2.5rem;
  overflow: hidden;
  border-radius: 0.25rem;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.header-title {
  margin-left: 0.625rem;
  font-size: 1.125rem;
  font-weight: bold;
  color: #333;
}

.questionnaire-content {
  padding: 0.9375rem;
}

.questionnaire-intro {
  margin-bottom: 1rem;
  padding: 0.75rem;
  background-color: #f0f9ff;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  color: #333;
  line-height: 1.5;
}

/* 表格样式 */
.questionnaire-table {
  width: 100%;
  margin-bottom: 1.25rem;

  table {
    width: 100%;
    border-collapse: collapse;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  }

  .mt-10 {
    margin-top: 0.625rem;
  }
}

.category-cell {
  width: 2.5rem;
  background-color: #f5f5f5;
  text-align: center;
  font-weight: bold;
  color: #333;
  font-size: 0.875rem;
  padding: 0.625rem 0.3125rem;
  line-height: 1.2;
}

.question-cell {
  width: 5.5rem;
  text-align: right;
  padding: 0.75rem 0.5rem;
  color: #333;
  font-size: 0.875rem;
  background-color: #f9f9f9;
}

.answer-cell {
  padding: 0.5rem;
}

/* 操作按钮区域 */
.action-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 1rem 0;
}

.toggle-section {
  display: flex;
  align-items: center;
}

.toggle-label {
  margin-left: 0.5rem;
  font-size: 0.875rem;
  color: #333;
}

.add-more-container {
  display: flex;
  justify-content: flex-end;
}

/* 备注区域 */
.notes-section {
  margin: 1rem 0;
  padding: 0.75rem;
  background-color: #fff;
  border-radius: 0.5rem;
  border: 1px solid #eee;
}

.notes-title {
  font-size: 0.875rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 0.5rem;
}

.notes-content {
  font-size: 0.875rem;
  color: #666;
  line-height: 1.5;
}

.submit-container {
  margin-top: 1.25rem;
  padding: 0 0.9375rem;
}

.dialog-content {
  padding: 1.25rem 0.9375rem;
  text-align: center;
  font-size: 0.9375rem;
}

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

/* 自定义 Vant 组件样式 */
:deep(.van-radio) {
  margin-right: 0.9375rem;
}

:deep(.van-radio__label) {
  color: #333;
  font-size: 0.875rem;
}

:deep(.van-button--primary) {
  background-color: #2196f3;
  border-color: #2196f3;
}

/* 移动端适配 */
@media screen and (max-width: 375px) {
  .question-cell {
    width: 4.5rem;
    font-size: 0.8125rem;
    padding: 0.625rem 0.375rem;
  }

  .category-cell {
    width: 2rem;
    font-size: 0.75rem;
  }

  :deep(.van-radio__label) {
    font-size: 0.8125rem;
  }

  :deep(.van-radio) {
    margin-right: 0.625rem;
  }
}

@media screen and (max-width: 320px) {
  .question-cell {
    width: 4rem;
    font-size: 0.75rem;
    padding: 0.5rem 0.3125rem;
  }

  .category-cell {
    width: 1.75rem;
    font-size: 0.6875rem;
  }

  :deep(.van-radio__label) {
    font-size: 0.75rem;
  }

  :deep(.van-radio) {
    margin-right: 0.5rem;
    transform: scale(0.9);
  }
}
</style>
