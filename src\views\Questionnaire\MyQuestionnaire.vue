<template>
  <div class="my-questionnaire">
    <div class="page-header">
      <div class="header-title">我的问卷</div>
      <div class="header-back" @click="goBack">
        <van-icon name="arrow-left" />
      </div>
    </div>

    <div class="questionnaire-list">
      <div v-if="questionnaireList.length === 0" class="empty-list">
        <div class="empty-icon">
          <van-icon name="description" size="48" />
        </div>
        <div class="empty-text">暂无问卷记录</div>
      </div>

      <div v-else>
        <div 
          v-for="(item, index) in questionnaireList" 
          :key="index" 
          class="questionnaire-item"
          @click="viewQuestionnaire(item)"
        >
          <div class="item-content">
            <div class="item-title">{{ item.title }}</div>
            <div class="item-info">
              <div class="item-date">提交时间：{{ formatDate(item.submitTime) }}</div>
              <div class="item-status" :class="getStatusClass(item.status)">
                {{ getStatusText(item.status) }}
              </div>
            </div>
          </div>
          <div class="item-arrow">
            <van-icon name="arrow" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { Toast } from 'vant';
import { storage } from '../../common';

export default {
  name: 'MyQuestionnaire',
  data() {
    return {
      questionnaireList: [
        {
          id: '1',
          title: '健康自测问卷',
          submitTime: new Date('2023-05-15'),
          status: 'completed'
        },
        {
          id: '2',
          title: '职业病危害问卷',
          submitTime: new Date('2023-06-20'),
          status: 'completed'
        }
      ]
    };
  },
  created() {
    // 这里可以添加获取用户问卷列表的API调用
    this.loadQuestionnaireList();
  },
  methods: {
    goBack() {
      this.$router.go(-1);
    },
    loadQuestionnaireList() {
      // 模拟从API获取数据
      // 实际项目中，这里应该调用后端API获取用户的问卷列表
      // 例如：
      // ajax.post(apiUrls.GetUserQuestionnaireList, {})
      //   .then(response => {
      //     this.questionnaireList = response.data.returnData;
      //   })
      //   .catch(error => {
      //     Toast('获取问卷列表失败，请稍后重试');
      //   });
    },
    formatDate(date) {
      if (!date) return '';
      const d = new Date(date);
      const year = d.getFullYear();
      const month = String(d.getMonth() + 1).padStart(2, '0');
      const day = String(d.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },
    getStatusText(status) {
      const statusMap = {
        'completed': '已完成',
        'pending': '待完成',
        'processing': '进行中'
      };
      return statusMap[status] || '未知状态';
    },
    getStatusClass(status) {
      return `status-${status}`;
    },
    viewQuestionnaire(item) {
      // 根据问卷类型跳转到不同的问卷页面
      if (item.title.includes('健康自测')) {
        this.$router.push({
          path: '/HealthQuestionnaire',
          query: { id: item.id, mode: 'view' }
        });
      } else if (item.title.includes('职业病')) {
        this.$router.push({
          path: '/OccupationalDiseasesQs',
          query: { id: item.id, mode: 'view' }
        });
      } else {
        this.$router.push({
          path: '/QuestionIndex',
          query: { id: item.id }
        });
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.my-questionnaire {
  width: 100%;
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-bottom: 20px;
}

.page-header {
  position: relative;
  height: 44px;
  line-height: 44px;
  text-align: center;
  background-color: #fff;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 10px;
}

.header-title {
  font-size: 18px;
  font-weight: 500;
  color: #323233;
}

.header-back {
  position: absolute;
  left: 16px;
  top: 0;
  font-size: 20px;
  color: #969799;
}

.questionnaire-list {
  padding: 0 16px;
}

.empty-list {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
}

.empty-icon {
  color: #dcdee0;
  margin-bottom: 10px;
}

.empty-text {
  font-size: 14px;
  color: #969799;
}

.questionnaire-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background-color: #fff;
  border-radius: 8px;
  margin-bottom: 10px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.item-content {
  flex: 1;
}

.item-title {
  font-size: 16px;
  font-weight: 500;
  color: #323233;
  margin-bottom: 8px;
}

.item-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.item-date {
  font-size: 12px;
  color: #969799;
}

.item-status {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 10px;
}

.status-completed {
  color: #07c160;
  background-color: rgba(7, 193, 96, 0.1);
}

.status-pending {
  color: #ff976a;
  background-color: rgba(255, 151, 106, 0.1);
}

.status-processing {
  color: #1989fa;
  background-color: rgba(25, 137, 250, 0.1);
}

.item-arrow {
  color: #c8c9cc;
  margin-left: 8px;
}

@media screen and (max-width: 768px) {
  .questionnaire-item {
    padding: 12px;
  }
  
  .item-title {
    font-size: 14px;
  }
  
  .item-date, .item-status {
    font-size: 10px;
  }
}
</style>
