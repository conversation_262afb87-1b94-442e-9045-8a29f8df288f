<template>
  <div class="occupational-disease-questionnaire">
    <div class="questionnaire-header">
      <h2>职业病问卷调查表</h2>
    </div>

    <div class="questionnaire-content">
      <!-- 基本信息部分 -->
      <div class="basic-info section">
        <div class="info-row">
          <div class="info-item">
            <span class="label">单位：</span>
            <van-field v-model="basicInfo.company" placeholder="请输入单位名称" />
          </div>
          <div class="info-item">
            <span class="label">体检日期：</span>
            <van-field v-model="basicInfo.examDate" readonly placeholder="请选择体检日期" @click="showCalendar('examDate')" />
          </div>
        </div>

        <div class="info-row">
          <div class="info-item">
            <span class="label">姓名：</span>
            <van-field v-model="basicInfo.name" placeholder="请输入姓名" />
          </div>
          <div class="info-item">
            <span class="label">性别：</span>
            <van-radio-group v-model="basicInfo.gender" direction="horizontal">
              <van-radio name="男">男</van-radio>
              <van-radio name="女">女</van-radio>
            </van-radio-group>
          </div>
          <div class="info-item">
            <span class="label">年龄：</span>
            <van-field v-model="basicInfo.age" type="number" placeholder="请输入年龄" />
          </div>
        </div>

        <div class="info-row">
          <div class="info-item">
            <span class="label">联系电话：</span>
            <van-field v-model="basicInfo.phone" placeholder="请输入联系电话" />
          </div>
          <div class="info-item">
            <span class="label">身份证号：</span>
            <van-field v-model="basicInfo.idCard" placeholder="请输入身份证号" />
          </div>
        </div>

        <div class="info-row">
          <div class="info-item">
            <span class="label">类别：</span>
            <van-checkbox-group v-model="basicInfo.category" direction="horizontal">
              <van-checkbox name="上岗前">上岗前</van-checkbox>
              <van-checkbox name="在岗期间">在岗期间</van-checkbox>
              <van-checkbox name="离岗时">离岗时</van-checkbox>
            </van-checkbox-group>
          </div>
        </div>
      </div>

      <!-- 一、职业史 -->
      <div class="work-history section">
        <div class="section-title">一、职业史</div>

        <div class="info-row">
          <div class="info-item">
            <span class="label">总工龄：</span>
            <van-field v-model="workHistory.totalYears" placeholder="请输入总工龄" />
          </div>
          <div class="info-item">
            <span class="label">接害工龄：</span>
            <van-field v-model="workHistory.hazardYears" placeholder="请输入接害工龄" />
          </div>
        </div>

        <div class="info-row">
          <div class="info-item full-width">
            <span class="label">毒害种类和名称：</span>
            <van-field v-model="workHistory.hazardTypes" placeholder="请输入毒害种类和名称" />
          </div>
        </div>

        <!-- 工作经历表格 -->
        <div class="work-table">
          <div v-for="(item, index) in workHistory.experiences" :key="index" class="work-experience-item">
            <div class="work-experience-header">
              <span class="work-experience-title">工作经历 #{{ index + 1 }}</span>
              <van-button v-if="workHistory.experiences.length > 1" size="mini" type="danger" @click="removeWorkExperience(index)">删除</van-button>
            </div>
            <table class="vertical-table">
              <tbody>
                <tr>
                  <th>起止日期</th>
                  <td>
                    <van-field class="work-field" v-model="item.period" readonly placeholder="请选择起止日期"
                      @click="showCalendar('period', index)" />
                  </td>
                </tr>
                <tr>
                  <th>工作单位</th>
                  <td>
                    <van-field class="work-field" v-model="item.company" placeholder="工作单位" />
                  </td>
                </tr>
                <tr>
                  <th>车间</th>
                  <td>
                    <van-field class="work-field" v-model="item.workshop" placeholder="车间" />
                  </td>
                </tr>
                <tr>
                  <th>工种</th>
                  <td>
                    <van-field class="work-field" v-model="item.jobType" placeholder="工种" />
                  </td>
                </tr>
                <tr>
                  <th>有害因素</th>
                  <td>
                    <van-field class="work-field" v-model="item.hazardFactors" placeholder="有害因素" />
                  </td>
                </tr>
                <tr>
                  <th>防护措施</th>
                  <td>
                    <van-field class="work-field" v-model="item.protection" placeholder="防护措施" />
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <div class="add-row">
            <van-button size="small" type="primary" @click="addWorkExperience">添加工作经历</van-button>
          </div>
        </div>
      </div>

      <!-- 二、既往病史 -->
      <div class="medical-history section">
        <div class="section-title">二、既往病史</div>
        <van-field v-model="medicalHistory.history" type="textarea" placeholder="请填写既往病史" rows="3" autosize />
      </div>

      <!-- 三、职业病史 -->
      <div class="occupational-history section">
        <div class="section-title">三、职业病史</div>

        <div class="info-row">
          <div class="info-item">
            <span class="label">病名：</span>
            <van-field v-model="occupationalHistory.diseaseName" placeholder="请输入病名" />
          </div>
          <div class="info-item">
            <span class="label">诊断日期：</span>
            <van-field v-model="occupationalHistory.diagnosisDate" readonly placeholder="请选择诊断日期"
              @click="showCalendar('diagnosisDate')" />
          </div>
        </div>

        <div class="info-row">
          <div class="info-item">
            <span class="label">诊断单位：</span>
            <van-field v-model="occupationalHistory.diagnosisUnit" placeholder="请输入诊断单位" />
          </div>
          <div class="info-item">
            <span class="label">是否痊愈：</span>
            <van-radio-group v-model="occupationalHistory.isRecovered" direction="horizontal">
              <van-radio name="是">是</van-radio>
              <van-radio name="否">否</van-radio>
            </van-radio-group>
          </div>
        </div>
      </div>

      <!-- 四、月经史 -->
      <div class="menstrual-history section" v-if="basicInfo.gender === '女'">
        <div class="section-title">四、月经史</div>

        <div class="info-row">
          <div class="info-item">
            <span class="label">初潮：</span>
            <van-field v-model="menstrualHistory.firstAge" placeholder="请输入年龄" />
            <span>岁</span>
          </div>
          <div class="info-item">
            <span class="label">经期：</span>
            <van-field v-model="menstrualHistory.periodDays" placeholder="请输入天数" />
            <span>天</span>
          </div>
          <div class="info-item">
            <span class="label">周期：</span>
            <van-field v-model="menstrualHistory.cycleDays" placeholder="请输入天数" />
            <span>天</span>
          </div>
          <div class="info-item">
            <span class="label">经年龄：</span>
            <van-field v-model="menstrualHistory.menopauseAge" placeholder="请输入年龄" />
            <span>岁</span>
          </div>
        </div>
      </div>

      <!-- 五、生育史 -->
      <div class="fertility-history section" v-if="basicInfo.gender === '女'">
        <div class="section-title">五、生育史</div>

        <div class="info-row">
          <div class="info-item">
            <span class="label">现有子女：</span>
            <van-field v-model="fertilityHistory.children" placeholder="请输入人数" />
            <span>人</span>
          </div>
          <div class="info-item">
            <span class="label">流产：</span>
            <van-field v-model="fertilityHistory.abortions" placeholder="请输入次数" />
            <span>次</span>
          </div>
          <div class="info-item">
            <span class="label">早产：</span>
            <van-field v-model="fertilityHistory.prematureBirths" placeholder="请输入次数" />
            <span>次</span>
          </div>
          <div class="info-item">
            <span class="label">死产：</span>
            <van-field v-model="fertilityHistory.stillbirths" placeholder="请输入次数" />
            <span>次</span>
          </div>
        </div>
      </div>

      <!-- 六、嗜好 -->
      <div class="habits section">
        <div class="section-title">烟酒史</div>

        <div class="info-row">
          <div class="info-item habits-item">
            <span class="label">吸烟情况：</span>
            <van-radio-group v-model="habits.smokingType" direction="horizontal">
              <van-radio name="不吸烟">不吸烟</van-radio>
              <van-radio name="偶吸烟">偶吸烟</van-radio>
              <van-radio name="轻微吸烟">轻微吸烟</van-radio>
            </van-radio-group>
          </div>
        </div>

        <div class="info-row" v-if="habits.smokingType && habits.smokingType !== '不吸烟'">
          <div class="info-item habits-item">
            <span class="label">吸烟量：</span>
            <van-field class="habits-field" v-model="habits.smokingAmount" placeholder="请输入" />
            <span>根/天，共</span>
            <van-field class="habits-field" v-model="habits.smokingYears" placeholder="请输入年数" />
            <span>年，戒烟</span>
            <van-field class="habits-field" v-model="habits.smokingQuitYears" placeholder="请输入年数" />
            <span>年</span>
          </div>
        </div>

        <div class="info-row">
          <div class="info-item habits-item">
            <span class="label">饮酒情况：</span>
            <van-radio-group v-model="habits.drinkingType" direction="horizontal">
              <van-radio name="不饮酒">不饮酒</van-radio>
              <van-radio name="偶饮酒">偶饮酒</van-radio>
              <van-radio name="经常饮酒">经常饮酒</van-radio>
            </van-radio-group>
          </div>
        </div>

        <div class="info-row" v-if="habits.drinkingType && habits.drinkingType !== '不饮酒'">
          <div class="info-item habits-item">
            <span class="label">饮酒量：</span>
            <van-field class="habits-field" v-model="habits.drinkingAmount" placeholder="请输入" />
            <span>ml/天，共</span>
            <van-field class="habits-field" v-model="habits.drinkingYears" placeholder="请输入年数" />
            <span>年，戒酒</span>
            <van-field class="habits-field" v-model="habits.drinkingQuitYears" placeholder="请输入年数" />
            <span>年</span>
          </div>
        </div>
      </div>

      <!-- 症状自述或问诊 -->
      <div class="symptoms section">
        <div class="section-title">症状自述或问诊</div>

        <!-- 单列症状列表 -->
        <symptoms-list-single :symptoms="symptoms" />


      </div>

      <!-- 签名部分 -->
      <div class="signature section">
        <div class="info-row">
          <div class="info-item full-width">
            <span class="label">受检者签名：</span>
          </div>
        </div>

        <div class="signature-pad-container" @click="showSignaturePopup">
          <div v-if="signature.patient" class="signature-preview">
            <img :src="signature.patient" alt="签名" class="signature-image" />
            <div class="signature-preview-text">点击重新签名</div>
          </div>
          <div v-else class="signature-placeholder">
            <van-icon name="edit" size="24" />
            <div>点击进行签名</div>
          </div>
        </div>

        <!-- 全屏签名弹出层 -->
        <van-popup v-model="showSignature" position="bottom" :style="{ height: '100%', width: '100%' }" closeable>
          <div class="fullscreen-signature" :class="{ 'landscape-mode': isLandscape }">
            <div class="fullscreen-signature-header">
              <div class="fullscreen-signature-title">请在空白处签名</div>
            </div>
            <div class="landscape-signature-container">
              <signature-canvas
                ref="signatureCanvas"
                :height="signatureHeight"
                :width="signatureWidth"
                @save="onSignatureSave"
                @clear="onSignatureClear"
              />
            </div>
          </div>
        </van-popup>

        <div class="info-row">
          <div class="info-item">
            <span class="label">日期：</span>
            <van-field v-model="signature.patientDate" readonly placeholder="请选择日期"
              @click="showCalendar('patientDate')" />
          </div>
        </div>

        <div class="note">
          <p>有上述症状者用"+"表示，可根据严重程度多个"+"表示，无症状用"-"表示</p>
        </div>
      </div>

      <!-- 提交按钮 -->
      <div class="submit-section">
        <van-button type="primary" block @click="submitForm">提交问卷</van-button>
      </div>
    </div>

    <!-- 日历弹出组件 -->
    <van-calendar v-model="showCalendarPopup" :min-date="minDate" :max-date="maxDate" @confirm="onCalendarConfirm" />

  </div>


</template>

<script>
import apiUrls from '../../config/apiUrls';
import { ajax, storage } from "../../common";
import SignatureCanvas from '../../components/SignatureCanvas';
import SymptomsListSingle from '../../components/SymptomsListSingle'
export default {
  name: 'OccupationalDiseasesQs',
  components: {
    SignatureCanvas,
    SymptomsListSingle
  },
  data() {
    return {
      // 日历相关
      showCalendarPopup: false,
      minDate: new Date(new Date().getFullYear() - 100, 0, 1),
      maxDate: new Date(new Date().getFullYear() + 1, 11, 31),
      currentField: '',
      currentIndex: -1,

      // 签名相关
      showSignature: false,
      signatureHeight: 300,
      signatureWidth: 600,
      isLandscape: false, // 是否横屏模式

      // 基本信息
      basicInfo: {
        company: '',
        examDate: '',
        name: '',
        gender: '男',
        age: '',
        phone: '',
        idCard: '',
        category: []
      },

      // 职业史
      workHistory: {
        totalYears: '',
        hazardYears: '',
        hazardTypes: '',
        experiences: [
          {
            period: '',
            company: '',
            workshop: '',
            jobType: '',
            hazardFactors: '',
            protection: ''
          }
        ]
      },

      // 既往病史
      medicalHistory: {
        history: ''
      },

      // 职业病史
      occupationalHistory: {
        diseaseName: '',
        diagnosisDate: '',
        diagnosisUnit: '',
        isRecovered: ''
      },

      // 月经史
      menstrualHistory: {
        firstAge: '',
        periodDays: '',
        cycleDays: '',
        menopauseAge: ''
      },

      // 生育史
      fertilityHistory: {
        children: '',
        abortions: '',
        prematureBirths: '',
        stillbirths: ''
      },

      // 嗜好
      habits: {
        smokingType: '', // 吸烟类型：不吸烟、偶尔吸、轻微吸烟
        smokingAmount: '', // 吸烟量（根/天）
        smokingYears: '', // 吸烟年数
        smokingQuitYears: '', // 戒烟年数
        drinkingType: '', // 饮酒类型：不饮酒、偶饮酒、经常饮酒
        drinkingAmount: '', // 饮酒量（ml/天）
        drinkingYears: '', // 饮酒年数
        drinkingQuitYears: '' // 戒酒年数
      },

      // 症状
      symptoms: {
        // 所有症状字段将在created钩子中初始化为"-"
      },

      // 签名
      signature: {
        patient: '', // 签名图片数据（base64）
        patientDate: ''
      }
    };
  },
  created() {
    // 初始化所有症状字段为"-"
    this.initializeSymptoms();
    this.getQuestion();

    // 计算签名板高度
    this.calculateSignatureHeight();
    window.addEventListener('resize', this.calculateSignatureHeight);
  },

  beforeDestroy() {
    window.removeEventListener('resize', this.calculateSignatureHeight);
    window.removeEventListener('orientationchange', this.calculateSignatureHeight);
    this.exitFullscreen();
  },
  methods: {
    // 初始化症状字段
    initializeSymptoms() {
      const symptomKeys = [
        'headache', 'dizziness', 'vertigo', 'insomnia', 'drowsiness', 'dreams',
        'memoryLoss', 'irritability', 'weakness', 'lowFever', 'nightSweats', 'hyperhidrosis',
        'fatigue', 'libidoDecrease', 'visionAbnormality', 'visionDecline', 'eyePain', 'photophobia',
        'tearing', 'visualImpairment', 'dryNose', 'nasalCongestion', 'nosebleed', 'rhinorrhea',
        'tinnitus', 'hearingLoss', 'badBreath', 'drooling', 'toothache', 'looseTeeth',
        'dryThroat', 'mouthOdor', 'oralUlcer', 'soreThroat', 'shortness', 'chestTightness',
        'chestPain', 'cough', 'sputum', 'hemoptysis', 'asthma', 'palpitation',
        'precordialDiscomfort', 'appetiteLoss', 'weightLoss', 'nausea', 'vomiting', 'abdominalDistension',
        'abdominalPain', 'diarrhea', 'abdominalFluid', 'constipation', 'frequentUrination', 'urgentUrination',
        'painfulUrination', 'lowerLimbPain', 'skinChanges', 'rash', 'edema', 'hairLoss',
        'jointPain', 'limbNumbness', 'movementDifficulty', 'menstrualAbnormality'
      ];

      // 将每个症状字段初始化为"-"
      symptomKeys.forEach(key => {
        this.$set(this.symptoms, key, '-');
      });
    },

    // 显示日历
    showCalendar(field, index = -1) {
      this.currentField = field;
      this.currentIndex = index;
      this.showCalendarPopup = true;
    },

    // 日历确认选择
    onCalendarConfirm(date) {
      // 格式化日期为 YYYY-MM-DD
      const formattedDate = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;

      // 根据当前字段设置值
      if (this.currentField === 'examDate') {
        this.basicInfo.examDate = formattedDate;
      } else if (this.currentField === 'diagnosisDate') {
        this.occupationalHistory.diagnosisDate = formattedDate;
      } else if (this.currentField === 'patientDate') {
        this.signature.patientDate = formattedDate;
      } else if (this.currentField === 'period' && this.currentIndex !== -1) {
        this.workHistory.experiences[this.currentIndex].period = formattedDate;
      }

      // 关闭日历
      this.showCalendarPopup = false;
    },

    // 添加工作经历行
    addWorkExperience() {
      this.workHistory.experiences.push({
        period: '',
        company: '',
        workshop: '',
        jobType: '',
        hazardFactors: '',
        protection: ''
      });
    },

    // 删除工作经历
    removeWorkExperience(index) {
      if (this.workHistory.experiences.length > 1) {
        this.workHistory.experiences.splice(index, 1);
      }
    },

    //同步體檢
    approveItem(regNo,ordinaryQs) {
      var pData={
        regno:regNo,
        kw:'2',
        questionZC:ordinaryQs
      }
      ajax.post(apiUrls.SaveQuestions, pData, { nocrypt: true }).then(r=>{

   
      Toast(`同步成功`);
      })
      
    
    },

    // 提交表单
    submitForm() {
      // 这里可以添加表单验证逻辑

      // 构建提交的数据
      const formData = {
        basicInfo: this.basicInfo,
        workHistory: this.workHistory,
        medicalHistory: this.medicalHistory,
        occupationalHistory: this.occupationalHistory,
        menstrualHistory: this.menstrualHistory,
        fertilityHistory: this.fertilityHistory,
        habits: this.habits,
        symptoms: this.symptoms,
        signature: this.signature
      };

      // 提交数据到后端
      console.log('提交的表单数据:', formData);
      this.$toast.success('问卷提交成功');
      var pData = {

        openId: storage.cookie.get("openid"),
        occupationalDiseasesQs: JSON.stringify(formData),


      };

      console.log(this.$route.query.regNo)
      this.approveItem(this.$route.query.regNo, JSON.stringify(formData))

      ajax.post(apiUrls.SaveGetQuestion, pData, { nocrypt: true }).then(r => {
        let res = r.data;
        console.log('SaveGetQuestion response:', r);

        if (res.success) {
          this.$toast.success('问卷提交成功');
          // 提交成功后重新获取问卷数据
          this.getQuestion();
          this.$router.push('/TeamBookSum')
        } else {
          this.$toast.fail('提交失败，请重试');
        }
      }).catch(error => {
        console.error('Error saving question data:', error);
        this.$toast.fail('提交失败，请重试');
      })

      // 这里可以添加实际的API调用
      // this.$http.post('/api/questionnaire/occupational-diseases', formData)
      //   .then(response => {
      //     this.$toast.success('问卷提交成功');
      //   })
      //   .catch(error => {
      //     this.$toast.fail('提交失败，请重试');
      //     console.error('提交失败:', error);
      //   });
    },
    getQuestion() {
      var pData = {
        openId: storage.cookie.get("openid"),
      };

      ajax.post(apiUrls.GetQuestion, pData, { nocrypt: true }).then(r => {
        var res = r.data;
        console.log('GetQuestion response:', res);

        // 检查是否有返回数据
        if (res.returnData && res.returnData.occupationalDiseasesQs) {
          try {
            const questionData = JSON.parse(res.returnData.occupationalDiseasesQs);
            this.copyQuestion(questionData);
          } catch (error) {
            console.error('Error parsing question data:', error);
            this.$toast.fail('获取问卷数据失败');
          }
        } else {
          console.log('No question data found, using default values');
          // 如果没有数据，使用默认值（已在created中初始化）
        }
      }).catch(error => {
        console.error('Error fetching question data:', error);
        this.$toast.fail('获取问卷数据失败');
      });
    },
    copyQuestion(qs) {
      // 检查每个字段是否存在，如果存在则赋值
      if (qs.basicInfo) this.basicInfo = qs.basicInfo;
      if (qs.workHistory) this.workHistory = qs.workHistory;
      if (qs.medicalHistory) this.medicalHistory = qs.medicalHistory;
      if (qs.occupationalHistory) this.occupationalHistory = qs.occupationalHistory;
      if (qs.menstrualHistory) this.menstrualHistory = qs.menstrualHistory;
      if (qs.fertilityHistory) this.fertilityHistory = qs.fertilityHistory;
      if (qs.habits) this.habits = qs.habits;
      if (qs.symptoms) this.symptoms = qs.symptoms;
      if (qs.signature) this.signature = qs.signature;

      // 如果有签名数据，显示在签名板上
      if (this.signature && this.signature.patient && this.$refs.signatureCanvas) {
        this.$nextTick(() => {
          this.$refs.signatureCanvas.setSignature(this.signature.patient);
        });
      }

      console.log('Question data copied successfully');
    },

    // 保存签名
    onSignatureSave(signatureData) {
      this.signature.patient = signatureData;
      this.$toast.success('签名已保存');
      this.exitFullscreen();
      this.showSignature = false; // 关闭弹出层
    },

    // 退出全屏模式
    exitFullscreen() {
      try {
        // 移除屏幕旋转事件监听
        window.removeEventListener('orientationchange', this.calculateSignatureHeight);

        // 解锁屏幕方向
        if (this.isMobileDevice() && window.screen && window.screen.orientation && window.screen.orientation.unlock) {
          window.screen.orientation.unlock();
        }

        // 退出全屏模式
        if (document.exitFullscreen) {
          document.exitFullscreen();
        } else if (document.webkitExitFullscreen) { /* Safari */
          document.webkitExitFullscreen();
        } else if (document.msExitFullscreen) { /* IE11 */
          document.msExitFullscreen();
        }
      } catch (error) {
        console.error('Failed to exit fullscreen mode:', error);
      }
    },

    // 清除签名
    onSignatureClear() {
      this.signature.patient = '';
    },

    // 显示签名弹出层
    showSignaturePopup() {
      this.showSignature = true;
      this.$nextTick(() => {
        // 当弹出层显示后再计算高度
        this.calculateSignatureHeight();

        // 如果有签名数据，显示在签名板上
        if (this.signature.patient && this.$refs.signatureCanvas) {
          this.$refs.signatureCanvas.setSignature(this.signature.patient);
        }

        // 尝试请求全屏模式
        this.requestFullscreen();
      });
    },

    // 请求全屏模式
    requestFullscreen() {
      try {
        const elem = document.documentElement;
        if (elem.requestFullscreen) {
          elem.requestFullscreen();
        } else if (elem.webkitRequestFullscreen) { /* Safari */
          elem.webkitRequestFullscreen();
        } else if (elem.msRequestFullscreen) { /* IE11 */
          elem.msRequestFullscreen();
        }

        // 屏幕旋转事件
        window.addEventListener('orientationchange', this.calculateSignatureHeight);

        // 如果是移动设备，尝试锁定屏幕方向为横屏
        if (this.isMobileDevice() && window.screen && window.screen.orientation && window.screen.orientation.lock) {
          // 尝试锁定屏幕方向为横屏
          window.screen.orientation.lock('landscape')
            .catch(error => {
              console.warn('Failed to lock screen orientation:', error);
            });
        }
      } catch (error) {
        console.error('Failed to enter fullscreen mode:', error);
      }
    },

    // 计算签名板尺寸
    calculateSignatureHeight() {
      // 获取窗口尺寸
      const windowWidth = window.innerWidth;
      const windowHeight = window.innerHeight;

      // 检测是否横屏模式
      // 使用屏幕宽高比检测
      this.isLandscape = windowWidth > windowHeight && windowWidth / windowHeight >= 1.2;

      // 如果支持屏幕旋转检测 API，使用更准确的方法
      if (window.screen && window.screen.orientation) {
        const orientationType = window.screen.orientation.type;
        if (orientationType.includes('landscape')) {
          this.isLandscape = true;
        } else if (orientationType.includes('portrait')) {
          this.isLandscape = false;
        }
      }

      if (this.isLandscape) {
        // 横屏模式：签名板铺满屏幕
        this.signatureWidth = windowWidth - 40; // 减去内边距
        this.signatureHeight = windowHeight - 120; // 减去标题和按钮的高度

        // 在移动设备上进一步减小高度，防止溢出
        if (this.isMobileDevice()) {
          this.signatureHeight = Math.min(this.signatureHeight, windowHeight - 150);
        }
      } else {
        // 竖屏模式：保持横向签名板
        this.signatureWidth = windowWidth - 40;
        this.signatureHeight = Math.min(windowHeight * 0.4, 300);
      }
    },

    // 检测是否为移动设备
    isMobileDevice() {
      return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    },

    // 获取症状名称
    getSymptomName(key, index) {
      const symptomNames = {
        'headache': '1. 头痛',
        'dizziness': '2. 头（颈）晕',
        'vertigo': '3. 眩晕',
        'insomnia': '4. 失眠',
        'drowsiness': '5. 嗣睡',
        'dreams': '6. 多梦',
        'memoryLoss': '7. 记忆力减退',
        'irritability': '8. 易激动',
        'weakness': '9. 感觉无力',
        'lowFever': '10. 低热',
        'nightSweats': '11. 盗汗',
        'hyperhidrosis': '12. 多汗',
        'fatigue': '13. 全身倦怠',
        'libidoDecrease': '14. 性欲减退',
        'visionAbnormality': '15. 视物特殊',
        'visionDecline': '16. 视力下降',
        'eyePain': '17. 眼痛',
        'photophobia': '18. 畏明',
        'tearing': '19. 流泪',
        'visualImpairment': '20. 视觉减退',
        'dryNose': '21. 鼻干',
        'nasalCongestion': '22. 鼻塞',
        'nosebleed': '23. 流鼻血',
        'rhinorrhea': '24. 流涕',
        'tinnitus': '25. 耳鸣',
        'hearingLoss': '26. 耳脑',
        'badBreath': '27. 口臭',
        'drooling': '28. 流涞',
        'toothache': '29. 牙痛',
        'looseTeeth': '30. 牙齿松动',
        'dryThroat': '31. 咽干',
        'mouthOdor': '32. 口腔异味',
        'oralUlcer': '33. 口腔溃疥',
        'soreThroat': '34. 咽痛',
        'shortness': '35. 气短',
        'chestTightness': '36. 胸闷',
        'chestPain': '37. 胸痛',
        'cough': '38. 咳嗽',
        'sputum': '39. 咳痰',
        'hemoptysis': '40. 呕血',
        'asthma': '41. 哮喘',
        'palpitation': '42. 心悲',
        'precordialDiscomfort': '43. 心前区不适',
        'appetiteLoss': '44. 食欲减退',
        'weightLoss': '45. 消瘦',
        'nausea': '46. 恶心',
        'vomiting': '47. 呕吐',
        'abdominalDistension': '48. 腹胀',
        'abdominalPain': '49. 腹痛',
        'diarrhea': '50. 腹泻',
        'abdominalFluid': '51. 腹水',
        'constipation': '52. 便秘',
        'frequentUrination': '53. 尿频',
        'urgentUrination': '54. 尿急',
        'painfulUrination': '55. 尿痛',
        'lowerLimbPain': '56. 下肢痛',
        'skinChanges': '57. 皮肤改变',
        'rash': '58. 皮疹',
        'edema': '59. 浮肿',
        'hairLoss': '60. 脱发',
        'jointPain': '61. 关节痛',
        'limbNumbness': '62. 四肢麻木',
        'movementDifficulty': '63. 动作不灵活',
        'menstrualAbnormality': '64. 月经异常'
      };

      return symptomNames[key] || `${index}. 未知症状`;
    },
  }
};
</script>

<style scoped>
.occupational-disease-questionnaire {
  padding: 15px;
  background-color: #f7f8fa;
}

.questionnaire-header {
  text-align: center;
  margin-bottom: 20px;
}

.questionnaire-header h2 {
  font-size: 20px;
  font-weight: bold;
}

.section {
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 15px;
  border-bottom: 1px solid #ebedf0;
  padding-bottom: 10px;
}

.info-row {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 10px;
}

.info-item {
  display: flex;
  align-items: center;
  margin-right: 15px;
  margin-bottom: 10px;
  flex: 1;
}

.info-item.full-width {
  flex: 100%;
}

.label {
  white-space: nowrap;
  margin-right: 5px;
  font-size: 14px;
}

.work-table {
  width: 100%;
  overflow-x: auto;
  margin-bottom: 15px;
  border-radius: 4px;
  background-color: #fff;
  padding: 5px;
}

table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 10px;
  border: 1px solid #dcdee0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

th,
td {
  border: 1px solid #dcdee0;
  padding: 10px 8px;
  text-align: center;
  font-size: 14px;
  vertical-align: middle;
  min-width: 120px;
}

/* 工作史表格的单元格宽度 */
.work-table td {
  min-width: 150px;
}

th {
  background-color: #f2f3f5;
  font-weight: bold;
  color: #323233;
}

/* 表格行交替背景色 */
tbody tr:nth-child(even) {
  background-color: #f8f8f9;
}

tbody tr:hover {
  background-color: #f0f1f2;
}

.add-row {
  margin-top: 10px;
  text-align: center;
}

.symptoms-list {
  width: 100%;
  overflow-x: auto;
  margin-bottom: 15px;
  border-radius: 4px;
  background-color: #fff;
  padding: 10px;
}

.symptoms-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f2f3f5;
}

.symptom-name {
  width: 150px;
  flex-shrink: 0;
  font-size: 14px;
  color: #323233;
  padding-right: 10px;
}

.symptom-value {
  flex: 1;
}

.symptom-field {
  width: 100%;
}

.note {
  font-size: 12px;
  color: #969799;
  margin-top: 15px;
}

.signature-pad-container {
  width: 100%;
  padding: 0 15px 15px;
  max-width: 600px;
  margin: 0 auto;
  cursor: pointer;
}

.signature-placeholder {
  height: 120px;
  border: 2px dashed #dcdee0;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #969799;
  background-color: #f7f8fa;
}

.signature-preview {
  position: relative;
  border: 2px solid #dcdee0;
  border-radius: 4px;
  overflow: hidden;
  background-color: #fff;
}

.signature-image {
  width: 100%;
  display: block;
}

.signature-preview-text {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  text-align: center;
  padding: 5px;
  font-size: 12px;
}

.fullscreen-signature {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #fff;
}

.landscape-mode {
  /* 横屏模式特殊样式 */
  justify-content: center;
}

.fullscreen-signature-header {
  padding: 15px;
  text-align: center;
  border-bottom: 1px solid #ebedf0;
}

.fullscreen-signature-title {
  font-size: 16px;
  font-weight: bold;
}

.landscape-signature-container {
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
  padding: 10px;
  overflow: auto;
}

/* 移动设备横屏模式下的特殊样式 */
@media screen and (max-width: 896px) and (orientation: landscape) {
  .fullscreen-signature-header {
    padding: 5px;
  }

  .fullscreen-signature-title {
    font-size: 14px;
  }

  .landscape-signature-container {
    padding: 5px;
  }

  .signature-actions {
    position: absolute;
    bottom: 10px;
    right: 10px;
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 4px;
    padding: 5px;
    z-index: 10;
  }
}

.submit-section {
  margin-top: 20px;
  padding: 0 15px;
}

/* 嗜好模块样式 */
.habits-item {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.habits-field {
  flex: 1;
  min-width: 80px;
  margin: 0 5px;
}

/* 职业史表格样式 */
.work-field {
  width: 100%;
  min-width: 100px;
}

.work-experience-item {
  margin-bottom: 20px;
  border: 1px solid #ebedf0;
  border-radius: 8px;
  overflow: hidden;
}

.work-experience-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background-color: #f2f3f5;
  border-bottom: 1px solid #ebedf0;
}

.work-experience-title {
  font-weight: bold;
  font-size: 14px;
  color: #323233;
}

.vertical-table {
  width: 100%;
  margin-bottom: 0;
}

.vertical-table th {
  width: 120px;
  text-align: right;
  background-color: #f8f8f9;
}

.vertical-table td {
  text-align: left;
}

@media screen and (min-width: 768px) {
  .habits-field {
    min-width: 120px;
  }

  .work-field {
    min-width: 150px;
  }
}

/* 移动设备适配 */
@media screen and (max-width: 768px) {
  .info-row {
    flex-direction: column;
  }

  .info-item {
    width: 100%;
    margin-right: 0;
  }

  th,
  td {
    padding: 5px;
    font-size: 12px;
  }

  .questionnaire-header h2 {
    font-size: 18px;
  }

  .section-title {
    font-size: 15px;
  }

  .label {
    font-size: 13px;
  }
}
</style>