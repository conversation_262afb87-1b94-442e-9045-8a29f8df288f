<template>
  <div>
    <div class="qtitle">
      <div style="background: #fdfcfe;">
        <div class="questBtn" style="">
          <img :src="`${publicPath}img/HomeLogo.jpg`" style="width: .95rem;" />
          <span style="margin-left: .1rem;">健康自测问卷评估</span>
        </div>
        <div style="font-size: 14px;width: 90%;margin: 0 auto;padding-top: 0.1rem;padding-bottom: 0.3rem;">
          <div>您好！</div>
          <div style="text-indent: 2em;">欢迎您到大连大学附属中山医院体检中心体检。请您认真完成健康体检调查问卷，以便我们为您提供更完善的健康评估及健康指导，感谢您的配合！</div>
        </div>
      </div>
      <div style="width: 100%;background: azure;margin-top: 0.1rem;">
        <div class="questionmation">
          <div class="questionBox">
            <div class="questionDiv">
              <div class="questionTitle" style="width: 40%;">
                <b>一、基本信息填写</b>
              </div>
              <div class="questionInput"></div>
            </div>

            <div class="questionDiv">
              <div class="questionTitle">
                <span>身高（cm）</span>
              </div>
              <div class="questionInput">
                <input type="text" placeholder="需填写" v-model="inputInfo.sg" maxlength="11" />
              </div>
            </div>
            <div class="questionDiv">
              <div class="questionTitle">
                <span>体重（kg）</span>
              </div>
              <div class="questionInput">
                <input type="text" placeholder="需填写" v-model="inputInfo.tz" maxlength="6" />
              </div>
            </div>

            <div class="questionDiv">
              <div class="questionTitle">
                <span>出生年月</span>
              </div>
              <div class="questionInput">
                <input type="text" placeholder="yyyy-MM-dd" v-model="inputInfo.birth" readonly @click="showDate = true" />
              </div>
            </div>


            <div class="questionDiv">
              <div class="questionTitle">
                <span>性别</span>
              </div>
              <div class="radioGroup">
                <van-radio-group v-model="inputInfo.sex" direction="horizontal">
                  <van-radio name="男">男</van-radio>
                  <van-radio name="女">女</van-radio>
                </van-radio-group>
              </div>
            </div>

            <div class="questionDiv">
              <div class="questionTitle">
                <span>民族</span>
              </div>
              <div class="radioGroup">
                <van-radio-group v-model="inputInfo.mz" direction="horizontal">
                  <van-radio name="汉族">汉族</van-radio>
                  <van-radio name="少数民族">少数民族</van-radio>
                </van-radio-group>
              </div>
            </div>

            <div class="questionDiv">
              <div class="questionTitle">
                <span>婚姻状况</span>
              </div>
              <div class="radioGroup">
                <van-radio-group v-model="inputInfo.hyzk" direction="horizontal">
                  <van-radio name="未婚">未婚</van-radio>
                  <van-radio name="已婚">已婚</van-radio>
                  <van-radio name="其他">其他</van-radio>
                </van-radio-group>
              </div>
            </div>

            <div style="margin-top: 0.2rem;"></div>
            <div class="questionDivB">
              <div class="questionTitleB">
                <span>文化程度</span>
              </div>
              <div class="radioGroup">
                <van-radio-group v-model="inputInfo.whcd" direction="horizontal">
                  <van-radio name="初中" style="margin-top: 0.2rem;margin-bottom: 0.1rem;">初中</van-radio>
                  <van-radio name="高中" style="margin-top: 0.2rem;margin-bottom: 0.1rem;">高中</van-radio>
                  <van-radio name="大学" style="margin-top: 0.2rem;margin-bottom: 0.1rem;">大学</van-radio>
                  <van-radio name="研究生以上" style="margin-top: 0.2rem;margin-bottom: 0.1rem;">研究生以上</van-radio>
                </van-radio-group>
              </div>
            </div>

            <div style="margin-top: 0.2rem;"></div>
            <div class="questionDivB">
              <div class="questionTitleB">
                <span>职业</span>
              </div>
              <div class="radioGroup">
                <van-radio-group v-model="inputInfo.zy" direction="horizontal">
                  <van-radio name="国家公务员" style="margin-top: 0.2rem;margin-bottom: 0.1rem;width: 50%;">国家公务员</van-radio>
                  <van-radio name="职员" style="margin-top: 0.2rem;margin-bottom: 0.1rem;">职员</van-radio>
                  <van-radio name="专业技术人员" style="margin-top: 0.2rem;margin-bottom: 0.1rem;width: 50%;">专业技术人员</van-radio>
                  <van-radio name="自由职业" style="margin-top: 0.2rem;margin-bottom: 0.1rem;width: 50%;">自由职业</van-radio>
                  <van-radio name="其他" style="margin-top: 0.2rem;margin-bottom: 0.1rem;">其他</van-radio>
                </van-radio-group>
              </div>
            </div>
            <div style="margin-top: 0.2rem;"></div>




          </div>
        </div>
        <!-- 出生日期选择器 -->
        <div v-if="showDate">
          <van-field v-model="inputInfo.birth" @click="showDate = true" placeholder="点击选择" readonly
            :rules="[{ required: true, message: '请选择出生日期' }]" />
          <van-popup v-model="showDate" position="bottom">
            <van-datetime-picker v-model="currentDate" title="选择日期" type="date" :min-date="minDate" :max-date="maxDate"
              @confirm="dateConfirm" @cancel="showDate = false" />
          </van-popup>
        </div>

        <div class="ljxiaybu">
          <div class="btnclik" @click="clicknext">
            <span>下一步</span>
          </div>
        </div>
        <div class="footDiv"></div>
      </div>


    </div>
  </div>
</template>
<script>
import { RadioGroup, Radio, Toast, Dialog } from "vant";
import apiUrils from '../../config/apiUrls'
import { ajax, storage, dataUtils } from '../../common';
export default {
  data() {
    return {
      publicPath: process.env.BASE_URL,
      page: "",
      inputInfo: {
        sex: "",
        birth: "",
        mz: "",
        whcd: "",
        hyzk: "",
        zy: "",
        sg: "",
        tz: ""
      },
      showDate: false,
      minDate: new Date(this.getDateFromYearsAgoOrAfter(-100)),
      maxDate: new Date(this.getDateFromYearsAgoOrAfter(100)),
      currentDate: new Date(this.getDateFromYearsAgoOrAfter(-25)),
    };
  },
  created() {
    this.page = this.$route.query['page'];
    if(storage.session.get("questionInfoZCA")){
      this.inputInfo=JSON.parse(storage.session.get("questionInfoZCA"));
    }
  },

  methods: {
    clicknext() {
      let u = this.inputInfo;
      if (u.sex && u.birth && u.mz && u.whcd && u.hyzk && u.zy && u.sg && u.tz) {
        storage.session.set("questionInfoZCA",JSON.stringify(this.inputInfo))
        this.$router.push({
          path: "/Questionnaire",
          query: {
            page: this.page
          }
        });
      } else {
        Toast("请完成基本信息！")
      }
    },
    dateConfirm(val) {
      // console.log("val", val);
      const originalDate = new Date(val);
      const nextDay = new Date(originalDate);
      nextDay.setDate(nextDay.getDate() + 1); // 增加一天
      this.inputInfo.birth = nextDay
        .toISOString()
        .slice(0, 10);
      this.showDate = false;
    },
    getDateFromYearsAgoOrAfter(years) {
      const date = new Date();
      date.setFullYear(date.getFullYear() + years);
      return `${date.getFullYear()}/${date.getMonth() + 1}/${date.getDate()}`;
    },
  }
};
</script>
<style lang="scss" scoped>
body {
  background: white !important;
}

.van-radio-group {
  display: flex;
}

.van-radio {
  margin-left: 0.2rem;
}

.qtitle {
  width: 100%;
  // height: 1rem;
  // background: wheat;
}

.questionmation {
  width: 100%;
  /* height: 4.35rem; */
  background: white;
  position: relative;
  top: 0;
  left: 0;
  box-sizing: border-box;
}

.questionmation .questionBox {
  width: 90%;
  height: 100%;
  margin: 0 auto;
  /* border: 1px solid; */
}

.questionBox .questionDiv {
  width: 100%;
  height: 0.87rem;
  display: flex;
  border-top: 1px solid #dfe3e9;
  box-sizing: border-box;
  font-size: 0.28rem;
}

.questionBox div:nth-child(1) {
  border: 0;
}

.questionDiv .questionTitle {
  width: 30%;
  height: 100%;
  display: flex;
  align-items: center;
  color: #4a4a4a;
}

.questionDiv .questionInput {
  width: 70%;
  height: 100%;
  display: flex;
  color: #9b9b9b;
  margin-right: 0.1rem;
}

.questionInput input {
  width: 100%;
  // height: 100%;
  border: none;
  outline: medium;
  text-align: right;
}

.questionBox .questionDivB {
  width: 100%;
  // height: 0.87rem;
  display: flex;
  border-top: 1px solid #dfe3e9;
  box-sizing: border-box;
  font-size: 0.28rem;
}

.questionDivB .questionTitleB {
  width: 30%;
  // height: 100%;
  display: flex;
  align-items: center;
  color: #4a4a4a;
}

.radioGroup {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  width: 70%;
  height: 100%;

  color: #9b9b9b;
  margin-right: 0.1rem;
}

.ljxiaybu {
  width: 100%;
  height: 2.16rem;
  left: 0;
  bottom: 0;
  display: flex;
  font-size: .24rem;
  background: #ffffff;
  border-top: 1px solid #DFE3E9;
  -webkit-box-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  align-items: center;
}

.btnclik {
  color: aliceblue;
  font-size: 16px;
  letter-spacing: 5px;
  background: #79a8ee;
  width: 80%;
  height: 1rem;
  border-radius: 15px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.questBtn {
  font-size: .5rem;
  color: #4a4a4a;
  text-align: center;
  font-weight: 600;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 1.3rem;
}
</style>