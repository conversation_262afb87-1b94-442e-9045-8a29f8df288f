<template>
  <div style class="qusetinoheard">
    <div id="dcwq" class="whole">
      <!--头部信息-->
      <div class="head">
        <div class="t_pic">
          <img :src="`${publicPath}img/HomeLogo.jpg`" />
        </div>
        <div class="t_tit">
          <p>健康自测问卷评估</p>
        </div>
        <!-- <h3
          style="padding-top: 40px;padding-left: 25vw;font-size: 130%;color:white"
        >{{questionList[chooseIndex].htitle}}</h3>-->
      </div>

      <!--选择单选题目-->
      <div class="t_con">
        <div class="con_tit">
          <b v-if="questionData.title" style="color: #cc3736;font-size: 0.33rem;">
            {{ questionData.title }}
            <span v-if="questionData.phy_exp != null">({{ questionData.phy_exp }})</span>
          </b>
          <p>
            <span>{{ wt_index + 1 }}.</span>
            {{ questionData.questions }}
            <!-- <span v-if="questionData.phy_exp!=null">({{questionData.phy_exp}})</span> -->
            <span v-if="questionData.type == '1'">(单选)</span>
            <span v-else-if="questionData.type == '2'">(多选)</span>
            <span v-else-if="questionData.type == '3'">(填空)</span>
          </p>
        </div>
        <div class="con_con">
          <ul>
            <li v-for="(item, index) in questionData.answers" :key="index">
              <div v-if="questionData.type == '3'" style="display: flex;">
                <van-field v-model="radio" placeholder="请输入信息" />
              </div>
              <div v-else-if="questionData.type == '1'">
                <van-radio-group v-model="radio">
                  <van-radio :name="item">{{ item.ans }}</van-radio>
                </van-radio-group>
              </div>
              <div v-else-if="questionData.type == '2'">
                <van-checkbox-group v-model="result">
                  <van-checkbox :name="item">{{ item.ans }}</van-checkbox>
                </van-checkbox-group>
              </div>
            </li>
          </ul>
        </div>
      </div>
      <!--上提下题按钮-->
      <div class="t_btn">
        <van-button color="linear-gradient(to right, rgb(231 145 57), rgb(238 136 10))"
          v-show="wt_index > 0 ? true : false" @click="gotoQusestion()">
          返回上一题
        </van-button>
        <van-button color="linear-gradient(to right, #2196f3, #2196f3)" @click="nextQuestions()">
          下一题
        </van-button>
      </div>
      <!--提示遮罩层-->
      <div class="ts_mask">
        <div class="tishi">
          <div class="qd" @click="maskfn()"></div>
        </div>
      </div>
      <van-dialog v-model="show_wj" show-cancel-button :beforeClose="beforeClose_wj">
        <div style="margin: 0.5rem .5rem;display:flex;justify-content: center;">您已完成问卷，是否提交？</div>
      </van-dialog>
    </div>
    <!--遮罩层-->
    <van-overlay :show="show" v-show="show">
      <div class="vanoverBtn">
        <van-loading type="spinner" color="#1989fa">获取中...</van-loading>
      </div>
    </van-overlay>
  </div>
</template>
<script>
import { storage } from "../../common";
import { Dialog, Toast } from "vant";
// import apiUrils from "../../config/apiUrls";
import $ from "jquery";
export default {
  data() {
    return {
      show: false,
      type: "",
      publicPath: process.env.BASE_URL,
      page: "",
      questionUser: {},
      show_wj: false,
      questionData: [],//展示的题目
      questions: [],//答题顺序
      questionInfo: [],//问卷所有题目
      questionList: [],//现在所答的问题
      chooseIndex: 0,
      wt_index: 0,//问题序号
      questionAnswer: [],//问卷答案
      result: [],//多选择题答案
      radio: "",//单选答案
      wt_next: [],//如果选中的答案有多个下一小问，则存放这

    };
  },
  created() {
    this.page = this.$route.query['page'];
    let u = JSON.parse(storage.session.get("questionInfoZCA"));
    this.questionUser = [{
      Q: "性别",
      A: u.sex
    }, {
      Q: "出生年月",
      A: u.birth
    }, {
      Q: "民族",
      A: u.mz
    }, {
      Q: "文化程度",
      A: u.whcd
    }, {
      Q: "婚姻状况",
      A: u.hyzk
    }, {
      Q: "职业",
      A: u.zy
    }, {
      Q: "身高",
      A: u.sg
    }, {
      Q: "体重",
      A: u.tz
    }]
    // this.type = storage.session.get("type");
    this.GetQuestionData();
  },
  mounted() {
    window.addEventListener("scroll", this.scrollToTop);
  },
  destroyed() {
    window.removeEventListener("scroll", this.scrollToTop);
  },
  methods: {

    GetQuestionData() {
      try {
        let b = [{
          title: "二、健康史—家族史",
          questions: "您的父母或兄弟姐妹是否患有明确诊断的疾病?",
          type: 1,
          wt_type: "A",
          answers: [{ ans: "是", isNext: "210" }, { ans: "否" }],
          answer: "",
        }, {
          code: "210",
          wt_type: "B",
          type: 2,
          questions: "请选择疾病名称?",
          answers: [{ ans: "高血压病" }, { ans: "脑卒中" }, { ans: "冠心病" }, { ans: "糖尿病" }, { ans: "心力衰竭" }, { ans: "慢性肾脏疾病" }, { ans: "痛风" }, { ans: "骨质疏松" }, { ans: "哮喘病" }, { ans: "胰腺炎" }, { ans: "外周血管病" }, { ans: "结核病" },
          { ans: "癌症", isNext: "211" }, { ans: "其他病" }],
          answer: [],
        }, {
          code: "211",
          wt_type: "C",
          type: 2,
          questions: "若选择答案中癌症，请确定所患的癌症名称?",
          answers: [{ ans: "肺癌" }, { ans: "肝癌" }, { ans: "胃癌" }, { ans: "结直肠癌" }, { ans: "胰腺癌" }, { ans: "骨癌" }, { ans: "白血病" }, { ans: "脑瘤" }, { ans: "乳腺癌" }, { ans: "前列腺癌" }, { ans: "其他癌" }],
          answer: [],
        }, {
          wt_type: "B",
          code: "210",
          questions: "您的父亲是否在55岁，母亲在65岁之前患有上述疾病?",
          type: 1,
          answers: [{ ans: "是" }, { ans: "否" }]
        }]
        this.questionInfo.push(b);

        let c = [{
          title: "三、健康史—现病史",
          questions: "您是否患有明确诊断的疾病或异常?",
          type: 1,
          wt_type: "A",
          answers: [{ ans: "是", isNext: "310" }, { ans: "否" }],
          answer: "",
        }, {
          code: "310",
          wt_type: "B",
          type: 2,
          questions: "请您确认具体疾病或异常的名称?",
          answers: [{ ans: "高血压病" }, { ans: "脑卒中" }, { ans: "冠心病" }, { ans: "糖尿病" }, { ans: "心力衰竭" }, { ans: "慢性肾脏疾病" }, { ans: "痛风" }, { ans: "骨质疏松" }, { ans: "哮喘病" }, { ans: "胰腺炎" }, { ans: "外周血管病" }, { ans: "结核病" },
          { ans: "癌症", isNext: "311" }, { ans: "其他病" }],
          answer: [],
        }, {
          code: "311",
          wt_type: "C",
          type: 2,
          questions: "若选择答案中癌症，请确定所患的癌症名称?",
          answers: [{ ans: "肺癌" }, { ans: "肝癌" }, { ans: "胃癌" }, { ans: "结直肠癌" }, { ans: "胰腺癌" }, { ans: "骨癌" }, { ans: "白血病" }, { ans: "脑瘤" }, { ans: "乳腺癌" }, { ans: "前列腺癌" }, { ans: "其他癌" }],
          answer: [],
        }, {
          wt_type: "B",
          code: "310",
          questions: "您的父亲是否在55岁，母亲在65岁之前患有上述疾病?",
          type: 1,
          answers: [{ ans: "是" }, { ans: "否" }]
        }]
        this.questionInfo.push(c);
        let d = [{
          title: "四、健康史—过敏史",
          questions: "您是否出现过过敏?",
          type: 1,
          wt_type: "A",
          answers: [{ ans: "是", isNext: "d10" }, { ans: "否" }],
          answer: "",
        }, {
          code: "d10",
          wt_type: "B",
          type: 2,
          questions: "请选择过敏原",
          answers: [{ ans: "青霉素" }, { ans: "磺胺类" }, { ans: "链霉素" }, { ans: "头孢类" }, { ans: "鸡蛋" }, { ans: "牛奶" }, { ans: "海鲜" }, { ans: "花粉" }, { ans: "其他" }],
          answer: [],
        }]
        this.questionInfo.push(d);
        let e = [{
          title: "五、健康史—用药史",
          questions: "您是否长期服用药物？（连续服用6个月以上，平均每日服用一次以上）",
          type: 1,
          wt_type: "A",
          answers: [{ ans: "是", isNext: "e10" }, { ans: "否" }],
          answer: "",
        }, {
          code: "e10",
          wt_type: "B",
          type: 2,
          questions: "您长期服用哪些药物",
          answers: [{ ans: "降压药" }, { ans: "降糖药" }, { ans: "抗心律失常药" }, { ans: "解热镇痛药" }, { ans: "镇静剂或安眠药" }, { ans: "强的松类药物" }, { ans: "雌激素类药物" }, { ans: "中草药" }, { ans: "避孕药" },
          { ans: "抗抑郁药" }, { ans: "其他" }],
          answer: [],
        }]
        this.questionInfo.push(e);
        let f = [{
          title: "六、健康史—手术史",
          questions: "您是否因病进行过手术治疗?",
          type: 1,
          wt_type: "A",
          answers: [{ ans: "是", isNext: "f10" }, { ans: "否" }],
          answer: "",
        }, {
          code: "f10",
          wt_type: "B",
          type: 2,
          questions: "请您选择手术的部位",
          answers: [{ ans: "头颅（含脑）" }, { ans: "眼" }, { ans: "颈部或甲状腺" }, { ans: "胸部（含肺部）" }, { ans: "胃肠" }, { ans: "肝胆" }, { ans: "心脏（含心脏介入）" }, { ans: "乳腺" }, { ans: "前列腺" },
          { ans: "妇科" }, { ans: "其他" }],
          answer: [],
        }]
        this.questionInfo.push(f);

        let g = [{
          title: "七、健康史—月经生育史（女性填写）",
          questions: "您第一次来月经的年龄（岁）：",
          type: 3,
          wt_type: "A",
          answers: [{ ans: "" }],
          answer: "",
        }, {
          questions: "您是否绝经?",
          type: 1,
          wt_type: "A",
          answers: [{ ans: "是" }, { ans: "否" }],
          answer: "",
        }]
        if (this.questionUser[4].A != "未婚") {
          g.push({
            questions: "您的结婚年龄（岁）：",
            type: 3,
            wt_type: "A",
            answers: [{ ans: "" }],
            answer: "",
          })
        }
        let gg = [{
          questions: "您是否生育过?",
          type: 1,
          wt_type: "A",
          answers: [{ ans: "是", isNext: "g39" }, { ans: "否" }],
          answer: "",
        }, {
          code: "g39",
          wt_type: "B",
          type: 3,
          questions: "生第一胎的年龄（岁）：",
          answers: [{ ans: "" }],
        }, {
          code: "g39",
          wt_type: "B",
          type: 1,
          questions: "您的孩子是母乳喂养吗？",
          answers: [{ ans: "是" }, { ans: "否" }],
        }, {
          code: "g39",
          wt_type: "B",
          type: 1,
          questions: "您是否曾患有妊娠糖尿病？",
          answers: [{ ans: "是" }, { ans: "否" }],
        }]
        g = [...g, ...gg];
        if (this.questionUser[0].A == "女") {
          this.questionInfo.push(g);
        }

        let h = [{
          title: "八、躯体症状（最近3个月）",
          questions: "您最近感觉身体总体健康状况如何？",
          type: 1,
          wt_type: "A",
          answers: [{ ans: "好" }, { ans: "一般" }, { ans: "差" }],
          answer: "",
        }, {
          questions: "您最近感到疲劳乏力或周身明显不适吗？",
          type: 1,
          wt_type: "A",
          answers: [{ ans: "没有" }, { ans: "偶尔" }, { ans: "经常" }],
          answer: "",
        }, {
          questions: "您最近出现过不明原因的身体消瘦或体重减轻吗？ （体重减轻超过原体重的10%）",
          type: 1,
          wt_type: "A",
          answers: [{ ans: "是" }, { ans: "否" }],
          answer: "",
        }, {
          questions: "您最近身体有过明显的疼痛吗？（外伤除外）",
          type: 1,
          wt_type: "A",
          answers: [{ ans: "是", isNext: "h413" }, { ans: "否" }],
          answer: "",
        }, {
          questions: "具体疼痛的部位？",
          type: 2,
          code: "h413",
          wt_type: "B",
          answers: [{ ans: "头" }, { ans: "颈肩" }, { ans: "咽喉" }, { ans: "腰背" }, { ans: "胸部" }, { ans: "腹部" }, { ans: "四肢" }, { ans: "关节" }],
          answer: [],
        }, {
          questions: "您近期是否有胃肠道不适症状？",
          type: 1,
          wt_type: "A",
          answers: [{ ans: "是", isNext: "h514" }, { ans: "否" }],
          answer: "",
        }, {
          questions: "具体的不适症状",
          type: 2,
          code: "h514",
          wt_type: "B",
          answers: [{ ans: "腹痛或腹部不适" }, { ans: "排便次数增加" }, { ans: "黑便" }, { ans: "便中带血脓液或黏液" }, { ans: "便秘" }, { ans: "贫血、消瘦乏力等其他症状" }],
          answer: [],
        }, {
          questions: "您是否有中或重度心、肝、肾功能不全？",
          type: 1,
          wt_type: "A",
          answers: [{ ans: "是" }, { ans: "否" }],
          answer: "",
        }]
        this.questionInfo.push(h);

        let i = [{
          title: "九、生活习惯—饮食",
          questions: "您通常能够按时吃三餐吗？",
          type: 1,
          wt_type: "A",
          answers: [{ ans: "能" }, { ans: "基本能" }, { ans: "不能" }],
          answer: "",
        }, {
          questions: "您的饮食偏好",
          type: 2,
          wt_type: "A",
          answers: [{ ans: "油炸食品" }, { ans: "熏制、腌制类" }, { ans: "吃快餐" }, { ans: "吃零食" }, { ans: "甜点" }, { ans: "喝粥" }, { ans: "其他" }],
          answer: [],
        }, {
          questions: "您吃水果吗？",
          type: 1,
          wt_type: "A",
          answers: [{ ans: "不吃" }, { ans: "偶尔吃（1-2次/周）" }, { ans: "经常吃（3-5次/周）" }],
          answer: "",
        }]
        this.questionInfo.push(i);
        let j = [{
          title: "十、生活习惯—吸烟、饮酒",
          questions: "您吸烟吗？（持续吸烟1年以上）",
          type: 1,
          wt_type: "A",
          answers: [{ ans: "不吸" }, { ans: "吸烟", isNext: "j191" }, { ans: "吸烟已戒（戒烟1年以上）", isNext: "j191B" }, { ans: "被动吸烟（每天累计15分钟以上，且每周1天以上）" }],
          answer: "",
        }, {
          questions: "您通常每天吸多少支烟（含戒烟前）(单位：支)：",
          type: 3,
          code: "j191",
          wt_type: "B",
          answers: [{ ans: "" }],
          answer: "",
        }, {
          questions: "您通常每天吸多少支烟（含戒烟前）(单位：支)：",
          type: 3,
          code: "j191B",
          wt_type: "B",
          answers: [{ ans: "" }],
          answer: "",
        }, {
          questions: "您持续吸烟的年限（含戒烟前）(单位：年)：",
          type: 3,
          code: "j191",
          wt_type: "B",
          answers: [{ ans: "" }],
          answer: "",
        }, {
          questions: "您持续吸烟的年限（含戒烟前）(单位：年)：",
          type: 3,
          code: "j191B",
          wt_type: "B",
          answers: [{ ans: "" }],
          answer: "",
        }, {
          questions: "您戒烟多长时间了(单位：年)：",
          type: 3,
          code: "j191B",
          wt_type: "B",
          answers: [{ ans: "" }],
          answer: "",
        }, {
          questions: "您喝酒吗？（每周饮酒1次以上）",
          type: 1,
          wt_type: "A",
          answers: [{ ans: "不喝" }, { ans: "喝", isNext: "j201" }, { ans: "喝、现已戒酒（戒酒1年以上）", isNext: "j201B" }],
          answer: "",
        }, {
          questions: "您一般喝什么酒？",
          type: 1,
          code: "j201",
          wt_type: "B",
          answers: [{ ans: "白酒" }, { ans: "啤酒" }, { ans: "红酒" }, { ans: "什么都喝" }],
          answer: "",
        }, {
          questions: "您持续喝酒的年限（含戒酒前）(单位：年)：",
          type: 3,
          code: "j201",
          wt_type: "B",
          answers: [{ ans: "" }],
          answer: "",
        }, {
          questions: "您持续喝酒的年限（含戒酒前）(单位：年)：",
          type: 3,
          code: "j201B",
          wt_type: "B",
          answers: [{ ans: "" }],
          answer: "",
        }, {
          questions: "您戒酒多长时间了(单位：年)：",
          type: 3,
          code: "j201B",
          wt_type: "B",
          answers: [{ ans: "" }],
          answer: "",
        }]
        this.questionInfo.push(j);

        let k = [{
          title: "十一、生活习惯—运动锻炼",
          questions: "您参加运动锻炼吗？",
          type: 1,
          wt_type: "A",
          answers: [{ ans: "不参加" }, { ans: "偶尔参加", isNext: "k12" }, { ans: "经常参加（一周3次以上）", isNext: "k13" }],
          answer: "",
        }, {
          questions: "您常采用的运动锻炼方式:",
          type: 2,
          code: "k12",
          wt_type: "B",
          answers: [{ ans: "散步" }, { ans: "慢跑" }, { ans: "游泳" }, { ans: "球类" }, { ans: "舞蹈" }, { ans: "打拳" }, { ans: "登山" }, { ans: "其他" }],
          answer: [],
        }, {
          questions: "您坚持锻炼多少年了(单位：年)：",
          type: 3,
          code: "k12",
          wt_type: "B",
          answers: [{ ans: "" }],
          answer: "",
        }, {
          questions: "您常采用的运动锻炼方式:",
          type: 2,
          code: "k13",
          wt_type: "B",
          answers: [{ ans: "散步" }, { ans: "慢跑" }, { ans: "游泳" }, { ans: "球类" }, { ans: "舞蹈" }, { ans: "打拳" }, { ans: "登山" }, { ans: "其他" }],
          answer: [],
        }, {
          questions: "您坚持锻炼多少年了(单位：年)：",
          type: 3,
          code: "k13",
          wt_type: "B",
          answers: [{ ans: "" }],
          answer: "",
        }]
        this.questionInfo.push(k);

        let l = [{
          title: "十二、生活习惯—睡眠健康",
          questions: "您的睡眠如何？",
          type: 1,
          wt_type: "A",
          answers: [{ ans: "好" }, { ans: "一般" }, { ans: "差", isNext: "l13" }],
          answer: "",
        }, {
          questions: "若您选择睡眠差，您睡眠差的主要表现？",
          type: 2,
          code: "l13",
          wt_type: "B",
          answers: [{ ans: "入睡困难" }, { ans: "早醒" }, { ans: "多梦" }, { ans: "夜起" }, { ans: "熟睡睡觉短" }, { ans: "其他" }],
          answer: [],
        }, {
          questions: "影响您睡眠差的主要原因？",
          type: 2,
          wt_type: "B",
          code: "l13",
          answers: [{ ans: "工作压力大" }, { ans: "环境干扰" }, { ans: "身体不适或疾病" }, { ans: "药物" }, { ans: "倒班" }, { ans: "其他" }],
          answer: [],
        }, {
          questions: "您每天平均睡眠时间？",
          type: 1,
          wt_type: "A",
          answers: [{ ans: "小于5小时" }, { ans: "5-7小时" }, { ans: "7-9小时" }, { ans: "8-9小时" }],
          answer: "",
        }]
        this.questionInfo.push(l);

        let n = [{
          title: "十三、健康素养",
          questions: "您多长时间做一次体检？",
          type: 1,
          wt_type: "A",
          answers: [{ ans: "从来不做" }, { ans: "半年" }, { ans: "一年" }, { ans: "大于3年" }],
          answer: "",
        }, {
          questions: "您是否主动获取医疗保健知识？",
          type: 1,
          wt_type: "A",
          answers: [{ ans: "是" }, { ans: "否" }],
          answer: "",
        }, {
          questions: "答完该问卷后，您对自己的健康状况感觉如何？",
          type: 1,
          wt_type: "A",
          answers: [{ ans: "很好" }, { ans: "比较好" }, { ans: "一般" }, { ans: "不好或较差" }, { ans: "不好说" }],
          answer: "",
        }, {
          questions: "您对该健康自测问卷的总体印象是？",
          type: 1,
          wt_type: "A",
          answers: [{ ans: "很好" }, { ans: "比较好" }, { ans: "一般" }, { ans: "不好或较差" }, { ans: "不好说" }],
          answer: "",
        }]
        this.questionInfo.push(n);

      } catch (error) {
        console.log(error);
      }

      this.questionList = this.questionInfo[this.chooseIndex];
      this.wt_next = this.questionList.filter(x => x.wt_type == "A");
      this.questionData = this.wt_next.shift();
      this.getAnswer();
    },
    nextQuestions() {
      let that = this;
      let an = {
        type: that.questionData.type,
        questions: that.questionData.questions,
      };
      if (that.questionData.type === 2) {
        if (that.result.length === 0) {
          Toast("请完成多选题");
          return;
        }
        an.answer = that.result;
      } else {
        if (that.radio.length === 0) {
          Toast("请完问题");
          return;
        }
        an.answer = that.radio;
      }

      let arry = [];
      //填空题暂没有小问
      //单选
      if (an.type === 1) {
        if (an.answer.isNext) {
          //如有小问，先答小问
          that.questionList.filter(x => {
            if (x.code == an.answer.isNext) {
              arry.push(x);
              // if (that.questionData.wt_type == "B") {
              //   that.wt_next.unshift(x)
              // } else {
              //   that.wt_next.push(x)
              // }
            }
          });
        }
      }
      //多选
      else if (an.type === 2) {
        //是否有小问
        let items = an.answer.filter(x => x.isNext);
        if (items.length > 0) {
          for (let i = 0; i < items.length; i++) {
            that.questionList.filter(x => {
              if (x.code == items[i].isNext) {
                arry.push(x);
              }
            });
          }
        }
      }
      that.wt_next = [...arry, ...that.wt_next]
      // console.log("that.wt_next1",that.wt_next);
      //备份问题，用于返回上一题；
      that.questionData.answer = an.answer;
      that.questions.push(that.questionData);
      that.questionAnswer.push(an);//存入答案

      if (that.wt_next.length > 0) {
        that.radio = "";
        that.result = [];
        that.wt_index++;
        this.questionData = that.wt_next.shift();
        this.getAnswer();
      }
      else if ((this.chooseIndex + 1) < this.questionInfo.length) {
        that.radio = "";
        that.result = [];
        this.chooseIndex++;
        this.questionList = this.questionInfo[this.chooseIndex];
        that.wt_next = this.questionList.filter(x => x.wt_type == "A");
        that.wt_index++;
        this.questionData = that.wt_next.shift();
        this.getAnswer();
        // console.log("this.questionAnswer", this.questionAnswer);
      }
      else {
        //完成问卷
        this.show_wj = true;
        return
        // console.log("this.questionAnswer", this.questionAnswer);
      }


    },
    //返回上一题
    gotoQusestion() {
      this.radio = "";
      this.result = [];
      if (this.questionData.title) {
        this.chooseIndex--;
        this.wt_next = [];
        this.questionList = this.questionInfo[this.chooseIndex];
      } else {
        this.wt_next.unshift(this.questionData);//把当前问题添加回wt_next
      }
      //返回上一题答案
      let items = this.questions.pop();
      // let items_wt_wt_next = JSON.parse(JSON.stringify(items));
      // items_wt_wt_next.answer = items_wt_wt_next.type === 2 ? [] : "";
      if (items.type === 2) {
        this.result = items.answer;
        for (let i = 0; i < items.answer.length; i++) {
          //除去上一题答案的小问
          if (items.answer[i].isNext) {
            this.wt_next = this.wt_next.filter(x => x.code != items.answer[i].isNext)
          }
        }
      } else {
        this.radio = items.answer;
        //除去上一题答案的小问
        if (items.answer.isNext) {
          this.wt_next = this.wt_next.filter(x => x.code != items.answer.isNext)
        }
      }
      this.questionAnswer.pop();
      this.wt_index--;
      this.questionData = items;
      // console.log("this.wt_next", this.wt_next);
    },
    //填写答案
    getAnswer() {
      if (this.questionData.type === 2) {
        this.result = this.questionData.answer;
      } else {
        this.radio = this.questionData.answer;
      }
    },
    //关闭提示
    maskfn() {
      $(".ts_mask").fadeOut(500);
    },
    beforeClose_wj(action, done) {
      if (action === 'confirm') {
        // console.log("this.questionUser", this.questionUser);
        // console.log("this.questionAnswer", this.questionAnswer);
        let a = JSON.parse(JSON.stringify(this.questionUser));
        this.questionAnswer.filter(x => {
          if (x.type === 1) {
            a.push({
              Q: x.questions,
              A: x.answer.ans
            })
          }
          else if (x.type === 2) {
            a.push({
              Q: x.questions,
              A: x.answer.map(y => y.ans).join(",")
            })
          }
          else {
            a.push({
              Q: x.questions,
              A: x.answer
            })
          }
        })
        // console.log("a", a);
        storage.session.set("questionInfoZC", JSON.stringify(a));
        switch (this.page) {
          case "person":
            this.$router.push({
              path: "/PersonIndex",
            });
            break;
          case "vehicle":
            this.$router.push({
              path: "/VehicleIndex",
            });
            break;
          case "staff":
            this.$router.push({
              path: "/StaffIndex",
            });
            break;
          // case "question":
          //   storage.session.set("type", "person");
          //   // window.location.href="http://hlhjkpg.krmanager.com"
          //   break;
          case "group":
            this.$router.push({
              path: "/TeamBookSum",
            });
            break;
        }




        Toast("感谢您的配合！")
        done()
        return;
      }
      this.show_wj = false;
      done();
    },
  }
};
</script>
<style lang="scss" scoped>
body,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
p,
blockquote,
dl,
dt,
dd,
ul,
ol,
li,
pre,
form,
fieldset,
form,
input,
legend,
button,
textarea {
  margin: 0;
  padding: 0;
  vertical-align: baseline;
  background: transparent;
  border: 0;
  outline: 0;
}

ul,
li {
  float: left;
  font-family: "微软雅黑";
  color: #6e5b2e;
}

body,
button,
input,
select,
textarea {
  font: 12px/1.5 tahoma, arial, \5b8b\4f53, sans-serif;
  background: #3299de;
  font-family: "微软雅黑";
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: 100%;
}

address,
cite,
dfn,
em,
var {
  font-style: normal;
}

code,
kbd,
pre,
samp {
  font-family: courier new, courier, monospace;
}

small {
  font-size: 12px;
}

ul,
ol {
  list-style: none;
}

a {
  text-decoration: none;
}

a:hover {
  text-decoration: none;
}

a:visited {
  color: #000;
}

sup {
  vertical-align: text-top;
}

sub {
  vertical-align: text-bottom;
}

legend {
  color: #000;
}

fieldset,
img {
  border: 0;
}

legend {
  color: #000;
}

fieldset,
img {
  border: 0;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

input[type="checkbox"] {
  -webkit-appearance: checkbox !important;
  appearance: checkbox !important;
}

button,
input,
select,
textarea {
  font-size: 100%;
  visibility: hidden;
  -webkit-appearance: none;
  appearance: none;
}

* {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

.qusetinoheard {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  overflow-y: auto;
  font: 12px/1.5 tahoma, arial, \5b8b\4f53, sans-serif;
  background: #fafafa;
  font-family: "微软雅黑";
}

/*调研首页*/
.whole {
  width: 100%;
  height: auto;
  margin: 0 auto;
  position: relative;
}

.content {
  width: 80%;
  height: auto;
  margin: 10vw 10% 0 10%;
}

.dc_tit {
  width: 100%;
  height: 30vw;
  position: relative;
  float: left;
  margin-left: -77vw;
}

.dc_tit img {
  width: 100%;
  height: auto;
}

.dc_pic {
  width: 100%;
  height: 80vw;
  position: relative;
  float: left;
  margin-bottom: 0vw;
}

.dc_pic img {
  width: 100%;
  height: auto;
}

.dc_btn {
  width: 100%;
  height: 20vw;
  position: relative;
  float: left;
  margin-left: 77vw;
}

.dc_btn input {
  width: 100%;
  height: 12vw;
  background: #fff;
  margin-top: 4vw;
  border-radius: 5px;
  font-size: 5vw;
  text-align: center;
  line-height: 12vw;
  -webkit-appearance: none;
  appearance: none;
  visibility: visible;
}

.dc_btn input span {
  font-size: 5vw;
}

.foot {
  width: 100%;
  height: 10vw;
  text-align: center;
  font-size: 3vw;
  color: #000;
  line-height: 10vw;
  float: left;
  margin-top: 8vw;
}

/*首页2*/
.dc_tit1 {
  width: 80%;
  height: 17vh;
  float: left;
  margin-left: 10vw;
  margin-top: 17vh;
  color: white;
  font-size: 3rem;
  text-align: center;
  position: relative;
  background: #0099ff;
}

.dc_tit1 img {
  width: 80%;
  height: auto;
  margin-left: 10%;
}

.pc_img {
  width: 100%;
  height: 100%;
  margin: 0 auto;
  position: absolute;
}

.pc_img img {
  width: 100%;
  height: auto;
}

.btn {
  width: 70%;
  height: 12vw;
  position: absolute;
  top: 128vw;
  left: 15vw;
}

/*调研内容页面*/
.head {
  width: 95%;
  height: 20vw;
  margin: 8vw auto 2vw 5%;
}

.t_pic {
  width: 20vw;
  height: 20vw;
  float: left;
  border-radius: 10px;
  overflow: hidden;
}

.t_pic img {
  width: 100%;
  height: auto;
}

.t_tit {
  width: 70vw;
  height: 8vw;
  margin-top: 1vw;
  float: right;
  border-bottom: 1px solid #b3b0b0;
  font-size: 4vw;
  color: #414141;
  letter-spacing: 0.5vw;
  overflow: hidden;
}

.t_tit p {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.t_con {
  width: 100%;
  height: auto;
  min-height: 55vh;
  margin: 0 auto;
  overflow: hidden;
  // background: url(../../assets/question/con_bg.png) right no-repeat;
  background-size: 50% auto;
}

.con_tit {
  width: 80%;
  height: auto;
  margin: 0 10%;
  color: #414141;
  position: relative;
  // margin-left: -70vw;
}

.con_tit p:nth-of-type(1) {
  font-size: 4.5vw;
  line-height: 5vw;
}

.con_tit p:nth-of-type(2) {
  font-size: 6vw;
  line-height: 10vw;
}

.con_con {
  width: 100%;
  height: auto;
  margin: 0 auto;
  float: left;
  position: relative;
}

.con_con ul {
  width: 80%;
  height: auto;
  margin: 6vw 10% 0 10%;
  overflow: hidden;
  position: relative;
}

.con_con ul li {
  width: 100%;
  height: auto;
  float: left;
  // top: 80vw;
  position: relative;
  color: #414141;
  font-size: 4.6vw;
  line-height: 6vw;
  margin: 3vw auto;
  z-index: 9;
}

.con_con ul li p {
  margin-left: 9vw;
}

.t_btn {
  width: 90%;
  height: 18vw;
  float: left;
  margin: 5vw 5% 10vw 5%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 1rem;
}

.t_btn button {
  width: 39%;
  height: 12vw;
  float: left;
  font-size: 4vw;
  text-align: center;
  line-height: 12vw;
  border-radius: 10px;
  visibility: visible;
}

.t_btn button:nth-of-type(1) {
  background: #fcb95a;
  color: #fff;
}

.t_btn button:nth-of-type(2) {
  background: #4c6fef;
  color: #fcf9f9;
  margin-left: 10%;
  float: right;
}

/*选择按钮*/
input[type="text"] {
  -webkit-appearance: none;
  appearance: none;
}

/**
 * Checkbox Four
 */
.checkboxFour {
  width: 0px;
  height: 0px;
  background-color: #fff;
  border-radius: 100%;
  position: relative;
  top: 5px;
}

/**
 * Create the checkbox button
 */
.checkboxFour label {
  display: block;
  width: 80vw;
  height: 6vw;
  -webkit-transition: all 0.2s ease;
  -moz-transition: all 0.2s ease;
  -o-transition: all 0.2s ease;
  -ms-transition: all 0.2s ease;
  transition: all 0.2s ease;
  cursor: pointer;
  top: -1vw;
  position: absolute;
  z-index: 1;
  // background: url(../../assets/question/huise.png) no-repeat;
  background-size: 6vw auto;
}

/**
 * Create the checked state
 */
.checkboxFour input[type="checkbox"]:checked+label {
  // background: url(../../assets/question/xz.png) no-repeat;
  background-size: 6vw auto;
}

/* RADIO */
.regular-radio {
  display: none;
}

.regular-radio+label {
  width: 20px;
  height: 20px;
  -webkit-appearance: none;
  appearance: none;
  /* background: url(img/icon2_w3c1.png) no-repeat; */
  background-size: 20px auto;
  transition: all 0.5s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05),
    inset 0px -15px 10px -12px rgba(0, 0, 0, 0.05);
  border-radius: 50px;
  display: inline-block;
  position: relative;
  top: 6px;
}

.regular-radio:checked+label:after {
  content: " ";
  width: 10px;
  height: 10px;
  border-radius: 50px;
  position: absolute;
  top: 4px;
  left: 4px;
  font-size: 32px;
}

.regular-radio:checked+label {
  /* background: url(../../assets/question/icon2_w3c2.png) no-repeat; */
  background-size: 20px auto;
  color: #99a1a7;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05),
    inset 0px -15px 10px -12px rgba(0, 0, 0, 0.05),
    inset 15px 10px -12px rgba(255, 255, 255, 0.1),
    inset 0px 0px 10px rgba(0, 0, 0, 0.1);
}

.regular-radio+label:active,
.regular-radio:checked+label:active {
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05),
    inset 0px 1px 3px rgba(0, 0, 0, 0.1);
}

.big-radio+label {
  padding: 16px;
}

.big-radio:checked+label:after {
  width: 20px;
  height: 20px;
  left: 4px;
  top: 4px;
}

/*遮罩层*/
.ts_mask {
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  position: fixed;
  z-index: 99;
  top: 0vw;
  display: none;
}

.tishi {
  width: 70%;
  height: 50vw;
  // background: url(../../assets/question/mxz_ts.png) center no-repeat;
  float: left;
  background-size: 100% auto;
  margin: 50vw 15%;
}

.qd {
  width: 50%;
  height: 10vw;
  margin: 30vw 25% 0 25%;
}

/*完成调研*/
.finish_ion {
  width: 100%;
  height: 50vw;
  margin: 0 auto;
}

.ion {
  width: 50%;
  height: 35vw;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  margin: 0 25%;
}

.ion img {
  width: 80%;
  height: auto;
  margin-top: 2.7vw;
  margin-left: 10%;
}

.wz {
  width: 100%;
  height: 15vw;
  font-size: 4vw;
  text-align: center;
  line-height: 15vw;
  letter-spacing: 0.3vw;
  font-family: "黑体";
}

.words {
  width: 100%;
  height: 40vw;
  margin: 0 auto;
  border-radius: 5px;
  background: #fff;
  position: relative;
}

.words textarea {
  width: 96%;
  height: 36vw;
  visibility: visible;
  background: #fff;
  margin-top: 2vw;
  color: #999;
  margin-left: 2%;
  overflow: hidden;
}

.words input {
  width: 26%;
  height: 8vw;
  background: #d8d8d8;
  position: absolute;
  z-index: 88;
  right: 3vw;
  top: 30vw;
  font-size: 4vw;
  line-height: 8vw;
  text-align: center;
  border-radius: 6px;
  visibility: visible;
}

.botton_t {
  width: 100%;
  height: 10vw;
  float: left;
  margin: 5vw auto;
}

.botton_t input {
  width: 100%;
  height: 14vw;
  background: #fcb95a;
  border-radius: 6px;
  text-align: center;
  line-height: 14vw;
  font-size: 5vw;
  color: #fff;
  visibility: visible;
}

.share {
  width: 90%;
  height: auto;
  margin: 0 5%;
}

.share img {
  width: 100%;
  height: auto;
}

.van-overlay {
  z-index: 999 !important;
}

.wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
}

.smokingYear {
  height: 40px;
  width: 100%;
  font-size: 18px;
  color: #4a4a4a;
  display: flex;
  align-items: flex-end;
  border-bottom: 1px solid #9c9c9c;
  margin-top: 20px;
}

.smokingYear>div {
  color: #4a4a4a;
  height: 30px;
  line-height: 30p;
  display: flex;
  align-items: flex-end;
}

.dsa {
  height: 30px;
  min-width: 40%;
  visibility: visible !important;
  width: 18px;
  background: white !important;
}

.divboom {
  width: 94%;
  margin-left: 3%;
  height: 24%;
  // background: firebrick;
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.divboom>button {
  visibility: visible !important;
  width: 35%;
  height: 65%;
  color: white;
}

.van-cell__value {
  margin-top: 0.8rem;
}

.van-overlay {
  display: flex;
  justify-content: center;
  align-items: center;
}

.vanoverBtn {
  display: flex;
  width: 50%;
  height: 1rem;
  background-color: #fdfdfd;
  justify-content: center;
  align-items: center;
  border-radius: 30px;
}
</style>
<style lang="scss">
.con_con .van-field__label {
  width: 17% !important;
  font-size: 17px !important;
}

.con_con .van-cell__value {
  border-bottom: 1px solid;
}
</style>