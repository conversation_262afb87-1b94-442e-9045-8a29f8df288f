<template>
    <div>
    <div class="content">
        <h1>尊敬的
            <span>{{report.name}}</span>
            <span>{{report.sex==1?"先生":"女士"}}</span>
        </h1>
            <div class="frist_p">
                        首先感谢您积极配合我们完成了本次医学检查，也衷心的感谢您对我们工作的支持和信任， 本报告是我们健康管理中心对您体检结果的分析汇总及建议指导。最终体检结果以纸质报告单为准。
            </div>
            <div class="second_p">
                        <p>感谢您积极配合。</p>
                        <p class="second_p_black">通过本报告，可以帮助您更好地了解自身的健康状况，掌管自身健康动向，及时发现存在的健康危险因素，进而通过健康指导、风险干预等方式进行疾病预防及健康管理</p>
                        <!-- <p class="second_p_black"></p> -->
            </div>
            <div class="third_p">
                        在此我们要提醒您注意的是由于医学技术发展的局限、个体间可能存在的生物差异以及您选择的项目并未涵盖全身所有脏器， 因此医生所做的健康诊断及医学建议仅仅是依据您的陈述和本次检查的结果而得出的，通常这对大多数人而言是确切的， 但鉴于任何一次医学检查的手段和方法都不具备绝对的特异性和灵敏度（即不存在100％的可靠和准确）这一事实， 我们建议您对异常的结果进行随访复查和其他相关的检查，便于医生有更多详实的医学证据去建立医学判断。 
            </div>
            <div class="fourth_p">
                        最后，我们欢迎并建议您每年至少来我中心进行一次系统检查，我们将为您提供历次体检结果对比，让您能够直观地了解自身近期的健康变化。
            </div>
        <div class="fifth_p">
            <h4>祝您</h4>
            <span class="second_p_black">身体健康</span>
        </div>
        <div class="last_p">{{tag}}管理中心</div>
    </div>
    <!--网页尾部-->
    <div class="footer">
        <div class="footer_left" @click="gotourl('/ReportMian')">
            <img src="../../../assets/report/<EMAIL>" alt="">
            <span>报告首页</span>
        </div>
        <div class="footer_right"  @click="gotourl('/ReportFinal')" >
            <span>总检报告</span>
            <img src="../../../assets/report/<EMAIL>" alt="">
        </div>
    </div>
    </div>
</template>

<script>
import {dataUtils, ajax, storage} from '@/common'
import {toolsUtils} from '@/common'
import apiUrls from '@/config/apiUrls';
import baseData from '@/config/baseData';
export default {
  components: {
  },
  data() {
    return {
      title: "导读",
      IsBack: false,
      report:{},
      tag:''
    };
  },
  created(){
      try {
         this.report=JSON.parse(storage.session.get('report'));
         this.tag=baseData.webtag;
      } catch (error) {
          
      }
  },
  mounted(){},
  methods: {
        gotourl(url)
        {
            this.$router.push({path:url});
        }
  },
  computed: {
    newtitle: function() {
      return this.title;
    }
  }
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>
p{
  margin: 0;
}
h1,h2,h3,h4,h5{
  margin: 0;
}
.content{
    width: 7.5rem;
    background: #FFFFFF;
    box-shadow: 0 2px 4px 0 #DFDFDF;
    margin-bottom:.88rem;
}
.content h1{
    // margin-top: .38rem;
    padding-top: .38rem;
    margin-left: .344rem;
    font-family: PingFangSC-Regular;
    font-size: .34rem;
    color: #646A6F;
}
.content h1>span{
    color: #51a3eb;
    line-height:.48rem;
        margin-left: 0.1rem;
}
.content .frist_p{
    width: 6.96rem;
    margin-top: .14rem;
    margin-left: .34rem;
    font-family: PingFangSC-Regular;
    font-size: .3rem;
    color: #737A80;
    line-height: .52rem;
    text-indent: .66rem;
}
.content .second_p{
    margin-top: .16rem;
    margin-left: .34rem;
    font-family: PingFangSC-Regular;
    font-size: .3rem;
    color: #737A80;
    line-height: .52rem;
    text-indent: .66rem;
}
.second_p_black{
    color: black;
}
.content .third_p{
    margin-top: .18rem;
    margin-left: .34rem;
    font-family: PingFangSC-Regular;
    font-size: .3rem;
    color: #737A80;
    line-height: .52rem;
    text-indent: .66rem;
}
.content .fourth_p{
    margin-top: .1rem;
    margin-left: .1rem;
    font-family: PingFangSC-Regular;
    font-size: .3rem;
    color: #737A80;
    line-height: .52rem;
    text-indent: .66rem;
}
.content .fifth_p{
    margin-top:.1rem;
    margin-left:.94rem;
    font-family: PingFangSC-Regular;
    line-height: .52rem;
    font-size: .3rem;
    color:#737A80;
}
.content .last_p{
    margin-top: .1rem;
    margin-left:4.22rem;
    padding-bottom: .42rem;
    font-family: PingFangSC-Regular;
    font-size: .3rem;
    color:black;
}

/*网页尾部*/
.footer{
    width: 7.5rem;
    height: .88rem;
    position: fixed;
    bottom: 0;
    left: 0;
}
.footer .footer_left{
    width: 3.73rem;
    height: .88rem;
    float: left;
    border-right: 1px solid white;
    background-color: #489EEA;
    display:flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}
.footer .footer_right{
    width: 3.73rem;
    height: .88rem;
    float: left;
    background-color: #489EEA;
    text-align: center;
    display:flex;
    align-items: center;
    justify-content: center;

}
.footer .footer_left img{
    width:.356rem;
    height:.304rem;
    display: inline-block;
}
.footer .footer_left span{
    font-family: PingFangSC-Medium;
    display: inline-block;
    /* width: 1.28rem; */
    height:.44rem;
    line-height: .44rem;
    font-size: .32rem;
    color: #FFFFFF;
    margin-left:.2rem;
}
.footer .footer_right img{
    width:.356rem;
    height:.304rem;
    display: inline-block;
    margin-left:.2rem;
}
.footer .footer_right span{
    font-family: PingFangSC-Medium;
    display: inline-block;
    /* width: 1.28rem; */
    height:.44rem;
    line-height: .44rem;
    font-size: .32rem;
    color: #FFFFFF;
}
</style>