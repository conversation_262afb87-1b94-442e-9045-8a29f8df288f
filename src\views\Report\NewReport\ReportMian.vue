<template>
    <div id="reportWrap">
        <!--页面头部-->
        <div class="header">
            <!--用户头像-->
            <div class="user_picture">
                <img src="../../../assets/report/user_pr.png" alt="">
            </div>
            <!--用户身份信息-->
            <div class="user_item">
                <ul>
                    <li>
                        <h3>姓名</h3>
                        <span>{{ report.name }}</span>
                    </li>
                    <li>
                        <h3>性别</h3>
                        <span v-if="report.sex == 1">男</span>
                        <span v-if="report.sex == 2">女</span>
                    </li>
                    <li>
                        <h3>年龄</h3>
                        <span>{{ report.age }}</span>
                    </li>
                </ul>
            </div>
        </div>
        <!--用户病例信息-->
        <div class="contanier">
            <div class="user_message">
                <ul>
                    <li>
                        <h2>单位名称</h2>
                        <span>|</span>
                        <p>{{ baseData.hospitalName }}</p>
                    </li>
                    <li>
                        <h2>体检机构</h2>
                        <span>|</span>
                        <p>健康管理中心</p>
                    </li>
                    <li>
                        <h2>体检日期</h2>
                        <span>|</span>
                        <p>{{ user.report_Date }}</p>
                    </li>
                    <li>
                        <h2>体检编号</h2>
                        <span>|</span>
                        <p>{{ user.report_No }}</p>
                    </li>
                </ul>
            </div>
        </div>
        <!--主页面导航-->
        <div class="nav">
            <div class="item" @click="gotourl('/reportguide')">
                <img src="../../../assets/report/introduction.png" alt="">
                <a href="javascript:void(0)">报告导读 </a>
            </div>
            <div class="item" @click="gotourl('/ReportFinal')">
                <img src="../../../assets/report/report2.png" alt="">
                <a href="javascript:void(0)">总检报告</a>
            </div>
            <div class="item" @click="gotourl('/ReportSuggest')">
                <img src="../../../assets/report/suggest.png" alt="">
                <a href="javascript:void(0)">医生建议</a>
            </div>
            <div class="item" @click="gotourl('/ReportDetails')">
                <img src="../../../assets/report/norm.png" alt="">
                <a href="javascript:void(0)">指标细节</a>
            </div>
            <div class="item" v-if='report.tj_cls != "1" && report.tj_cls != "2"' @click="reportPDF()">
                <img src="../../../assets/report/downloadPdf.png" alt="">
                <a href="javascript:void(0)">PDF下载</a>
            </div>
            
            <!-- <div class="item"  v-if="showMail" @click="gotourl('/report/reportMail/'+reportNo)" >
            <img src="@/assets/img/report2.png" alt="">
            <a href="javascript:void(0)">邮寄信息</a>
        </div> -->
        </div>
        <div class="pacsUrlBox">
            <div style="font-size: .35rem;font-weight: bold; text-align: center;">
                <p style="padding: .4rem;">云胶片 扫一扫</p>
                <img :src="pacsUrl" alt="" style="width: 2rem;height: 2rem;">
            </div>
           
            </div>
        <!--页面尾部-->
        <div class="foot">
            <span>注意：为了保障你的隐私资料，请勿随意传阅，最终体检结果以纸质报告单为准</span>
        </div>

        <!--网页尾部-->
        <div class="footer">
            <div class="footer_left" @click="gotourl('/ReportList')">
                <img src="../../../assets/report/repotlist.png" alt="">
                <span>报告列表</span>
            </div>
        </div>
    </div>
</template>

<script>
import { storage, ajax, toolsUtils } from '../../../common';
import apiUrls from '../../../config/apiUrls';
import Vue from "vue";
import { Toast } from "vant";
//import baseData from '../../../config/baseData';

export default {
    components: {
    },
    data() {
        return {
            baseData: Vue.prototype.baseData,
            pacsUrl: "",
            report: {
                name: '',
                sex: '',
                age: null,
            },
            user: [],
            reportNo: '',
            showMail: false,
            hospList: [
                {
                    hospName: "交通路店",
                    hospCode:"01",
                    hospTel: "0371-56533900",
                    hospAddr: "郑州蓝天健康体检服务院二七蓝天门诊部",
                    secretkey: "cb992b43-cb58-45d2-847f-8b6f7df007a9"
                },
                {
                    hospName: "商城路店",
                    hospCode: "02",
                    hospTel: "0371-55906231",
                    hospAddr: "郑州蓝天健康体检门诊部",
                    secretkey: "a4e6c740-a74d-4dee-8cfe-35ae69d7d0ad"

                },
                {
                    hospName: "纬四路店",
                    hospCode: "03",
                    hospTel: "0371-56533922",
                    hospAddr: "郑州蓝天健康体检综合门诊部",
                    secretkey: "b9703a98-7fab-4a32-9f01-c259f27799ad"
                },

                // 郑州蓝天健康体检门诊部（云胶片）2商城路 ,a4e6c740-a74d-4dee-8cfe-35ae69d7d0ad
                //这个貌似通用-----
                // 郑州蓝天健康体检服务院综合门诊部（云胶片）2花园路,b9703a98-7fab-4a32-9f01-c259f27799ad
                // 郑州蓝天健康体检服务院二七蓝天门诊部（云胶片）1交通路,cb992b43-cb58-45d2-847f-8b6f7df007a9
            ]
        }
    },
    created() {
        // try {
        //     //  var user=JSON.parse(storage.cookie.get("user"));
        this.user = JSON.parse(storage.session.get("pat_info"));

        console.log(this.user)

         this.GetPacsPhotoUrl(this.user.report_No,this.user.hosp_code.trim())
        // } catch (error) {
        //     this.$router.push({ path: "/oauth?type=jump" })
        // }
        // this.reportNo=toolsUtils.decrypt(this.$route.params.no,this.user.pat_code.trim()+'nysyt');
        this.GetReportDetail(this.user.report_No);
       
    },
    mounted() { },
    methods: {
        GetPacsPhotoUrl(report_No,code) {
  
            ajax.post(apiUrls.GetPacsPhotoUrl, {
                accessionNumber: report_No,
                organizationUuid: this.hospList.find(x=>x.hospCode==code).secretkey
            }).then(r => {
                if (r.data.resultCode == "200") {
                    console.log("pacsUrl",r.data.resultData)
                    var data = r.data.resultData;
                    console.log(data);
                    let url = data.photoUrl;

                    // 找到 `?` 在 URL 中的位置
                    let queryStringIndex = url.indexOf('?');

                    // 截取 `?` 前面的部分
                    this.pacsUrl = url.slice(0, queryStringIndex);
                    
                }
                else {
                    Toast(r.data.resultMsg);
                }
                return;
            }).catch(err => {
                Toast("网络异常");
                console.log(err);
            });
        }
        ,
        GetReportDetail(report_No) {
            var pData = {
                regNo: report_No,
            };
            ajax.post(apiUrls.GetReportDetail, pData, { nocrypt: true }).then(r => {
                if (r.data.success) {
                    var data = JSON.parse(r.data.returnData);
                    this.report = data;
                    storage.session.set('report', JSON.stringify(data));
                }
                else {
                    Toast(r.data.returnMsg);
                }
                return;
            }).catch(err => {
                Toast("网络异常");
                console.log(err);
            });
        },
        gotourl(url) {
            this.$router.push({ path: url });
        },
        reportPDF() {
            if (this.report == null || this.report == "" || this.report == undefined) {
                return;
            }
            //获取pdf报告下载地址
            let pdata = {
                kw: this.report.reg_date.trim(),
                regno: this.report.regno.trim()
            }
            ajax.post(apiUrls.GetReportPDFUrl, pdata, { nocrypt: true }).then(r => {
                r = r.data;
                if (r.success) {
                    var url = encodeURIComponent(r.returnData);
                    var that = this;
                    setTimeout(function () {
                        // console.log(that.baseData.apiHost + '/home/<USER>' + url);
                        //原因是window.open会中断正在执行的进程，这样能保证其它代码执行完成再执行这个。
                        window.location.href = that.baseData.apiHost + '/home/<USER>' + url; //改变页面的location
                    }, 300);
                } else {
                    Toast("网络异常");
                }
            }).catch(err => {
                Toast("网络异常");
            });
        },
    }
}
</script>

<style lang="scss" scoped>
p {
    margin: 0;
}

h1,
h2,
h3,
h4,
h5 {
    margin: 0;
}

#reportWrap {
    width: 100%;
    height: 100%;
    background-color: #f5f6f7;
}

/*页面头部*/
#reportWrap .header {
    width: 100%;
    height: 1.76rem;
    background: url(../../../assets/report/<EMAIL>);
    background-size: cover;
}

/*用户头像*/
#reportWrap .header .user_picture {
    width: .96rem;
    height: .96rem;
    overflow: hidden;
    position: absolute;
    margin-left: .4rem;
    margin-top: .4rem;
}

/*用户身份信息*/
#reportWrap .header .user_item {
    position: absolute;
    margin-left: 1.72rem;
    margin-top: .2rem;
}

#reportWrap .header .user_item>ul {
    overflow: hidden;
}

#reportWrap .header .user_item>ul>li {
    display: inline-block;
    font-family: PingFangSC-Regular;
    font-size: .24rem;
    color: #FFFFFF;
    line-height: .48rem;
}

#reportWrap .header .user_item>ul>li span {
    font-size: .32rem;
    height: .48rem;
}

#reportWrap .header .user_item>ul>li:nth-child(3n+2) {
    margin: 0 1rem;
}

/*用户病例信息*/
.contanier {
    width: 100%;
    height: 4rem;
    background: #FFFFFF;
    box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.10);
}

.user_message {
    position: absolute;
    margin-left: .36rem;
    margin-top: .16rem;
}

.user_message ul li {
    width: 7.02rem;
    height: .88rem;
    font-size: .28rem;
    font-family: PingFangSC-Regular;
    color: #4A4A4A;
}

.user_message ul li h2 {
    display: inline-block;
    line-height: .88rem;
    font-size: .28rem;
    color: #7A7A7A;
}

.user_message ul li span {
    display: inline-block;
    line-height: .88rem;
    font-size: .28rem;
    color: #7A7A7A;
    margin: 0 .2rem;
}

.user_message ul li p {
    display: inline-block;
    line-height: .88rem;
    font-size: .28rem;
    color: #4A4A4A;
}

/*主页面导航*/
.nav {
    /* width:7.5rem; */
    width: 100%;
    /* height: 2.4rem; */
    overflow: hidden;
    margin-top: .24rem;
    text-align: center;
}

.nav .item {
    /* width:calc(7.5rem/2); */
    width: 50%;
    height: 1.2rem;
    float: left;
    opacity: 0.99;
    font-family: PingFangSC-Medium;
    font-size: .32rem;
    color: #4A4A4A;
    background: #FFFFFF;
    box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.10);
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 1px solid rgba(0, 0, 0, 0.10);
}

.nav .item img {
    width: .48rem;
    height: .48rem;
    display: inline-block;
}

.nav .item a {
    height: .44rem;
    display: inline-block;
    margin-left: .28rem;
}


/*页面尾部*/
.foot {
    width: 7.02rem;
    height: 1rem;
    margin: 0 auto;
    position: relative;
    top: .22rem;
}

.foot span {
    font-family: PingFangSC-Regular;
    font-size: .26rem;
    color: #9B9B9B;
    display: block;
}


.footer {
    width: 7.5rem;
    height: .88rem;
    position: fixed;
    bottom: 0;
    left: 0;
}

.footer .footer_left {
    width: 100%;
    height: .88rem;
    float: left;
    border-right: 1px solid white;
    background-color: #489EEA;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.footer .footer_left img {
    width: .356rem;
    height: .304rem;
    padding-right: .1rem;
    display: inline-block;
}

.footer .footer_left span {
    font-family: PingFangSC-Medium;
    display: inline-block;
    height: .44rem;
    line-height: .44rem;
    font-size: .32rem;
    color: #FFFFFF;
}
</style>
