<template>
<div id="reportWrap">
        <!--头部部分-->
        <div class="header">
            <img src="../../../assets/report/<EMAIL>" alt="">
            <h2>总检</h2>
            <span>{{report.doc_name}}</span>
            <div class="toggle">
                <span>为了您的健康，我们提供以下健康建议，<br>如有疑问请联系我们。祝您身体健康！</span>
            </div>
        </div>
        <!--内容部分-->
        <div class="content">
            <div class="content_top">
                <h2><strong>健康建议</strong></h2>
            </div>
            <hr>
            <div class="content_bottom" v-if="sugglist.length>0" >
                <p>
                    <strong v-for="(item,index) in sugglist" :key="index">
                        {{item}} <br>
                    </strong>
                </p>
            </div>
            <div class="nothing" v-if="sugglist.length<1">
                <div>
                    <img src="../../../assets/report/nothing.png">
                    <span>没有数据</span>
                </div>
            </div>
        </div>
        <!--网页尾部-->
        <div class="footer">
            <div class="footer_left" @click="gotourl('/ReportFinal')">
                <img src="../../../assets/report/<EMAIL>" alt="">
                <span>总检报告</span>
            </div>
            <div class="footer_right" @click="gotourl('/ReportDetails')" >
                <span>指标明细</span>
                <img src="../../../assets/report/<EMAIL>" alt="">
            </div>
        </div>
</div>
</template>

<script>
import {dataUtils, ajax, storage} from '@/common'
import {toolsUtils} from '@/common'
import apiUrls from '@/config/apiUrls';
export default {
  components: {
  },
  data() {
    return {
      title: "首页",
      IsBack: false,
      report:{},
      sugglist:[],
    };
  },
  created(){
      try {
         this.report=JSON.parse(storage.session.get('report'));
      } catch (error) {
          
      }
       this.disposeRes()
  },
  mounted(){},
  methods: {
        gotourl(url)
        {
            this.$router.push({path:url});
        },
        disposeRes() {
           var res=this.report;
            this.sugglist=[];
            if (res.sugg_tag.indexOf("\r\n") != -1)
                this.sugglist = res.sugg_tag.split("\r\n");
            else
                this.sugglist.push(res.sugg_tag);
            //遍历删除空元素
            for (var i = 0; i < this.sugglist.length; i++) {
                if (this.sugglist[i] === "") {
                    this.sugglist.splice(i, 1);//删除一个元素
                    i--;
                }
            }
        }
  },
  computed: {
    newtitle: function() {
      return this.title;
    }
  }
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>
p{
  margin: 0;
}
h1,h2,h3,h4,h5{
  margin: 0;
}
.nothing{
	width: 100%;
}
.nothing div{
	width: 60%;
	margin: 10% auto 0;
}
.nothing div img{
	width: 100%;
}
.nothing div span{
	width: 100%;
	display: block;
	text-align: center;
	font-size: .48rem;
	color: #018BF0;
}
#reportWrap{
    width: 100%;
    height: 100%;
    background-color: #f5f6f7;
}
/*头部部分*/
#reportWrap .header{
    width: 100%;
    height: 1.44rem;
    position: relative;
    background: #FFFFFF;
    box-shadow: 0 1px 8px 0 #DFDFDF;
}
#reportWrap .header>img{
    width: .88rem;
    height: .88rem;
    display: inline-block;
    position: absolute;
    left:.2rem;
    top:.28rem;
}
#reportWrap .header>h2{
    display: inline-block;
    font-family: PingFangSC-Regular;
    font-size: .24rem;
    color: #9E9E9E;
    width: 1.36rem;
    height:.48rem;
    position: absolute;
    top: .38rem;
    left: 1.26rem;
}
#reportWrap .header>span{
    display: inline-block;
    width: 1.16rem;
    height:.42rem;
    font-family: PingFangSC-Regular;
    font-size: .24rem;
    color: #4A4A4A;
    position: absolute;
    top: .72rem;
    left: 1.26rem;
}
#reportWrap .header>.toggle{
    width: 4.96rem;
    height: 1rem;
    background: url("../../../assets/report/viewReportIcon2.png");
    background-size: 100% 100%;
    text-align: center;
    display:flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    left: 2.34rem;
    top: .22rem;
}
#reportWrap .header>.toggle>span{
    font-family: PingFangSC-Regular;
    font-size: .24rem;
    color: #018BF0;
    line-height: .32rem;
}
/*内容部分*/
.content{
    width: 100%;
    /* height: 5rem; */
    /* background: #FFFFFF;
    box-shadow: 0 2px 4px 0 #DFDFDF; */
}
.content .content_top{
    width: 7.5rem;
}

.content .content_top>h2{
    font-family: PingFangSC-Medium;
    font-size: .32rem;
    color: #646A6F;
    position: absolute;
    display: inline-block;
    width: 80%;
    height: .44rem;
    top: 1.96rem;
    left: .28rem;
}
.content .content_top>span{
    position: absolute;
    top: 2.3rem;
    left: 1.08rem;
}
.content .content_top>p{
    position: absolute;
    left: 5.5rem;
    top: 2.3rem;
}
.content hr{
    width: 7.02rem;
    height: .02rem;
    border: 0;
    margin: 0;
    padding: 0;
    position: absolute;
    top: 2.6rem;
    left: .26rem;
    background-color: #DFDFDF;
}
.content .content_bottom{
    width: 100%;
}
.content .content_bottom>img{
    display: inline-block;
    width: .39rem;
    height: .36rem;
    background-color: black;
    position: absolute;
    top: 3.18rem;
    left: .34rem;
}
.content .content_bottom>h1{
    font-family: PingFangSC-Regular;
    font-size: .32rem;
    color: #646A6F;
    display: inline-block;
    position: absolute;
    top: 3.18rem;
    left: .924rem;
}
.content .content_bottom>p{
    font-family: PingFangSC-Regular;
    font-size: .28rem;
    color: #737A80;
    line-height: .48rem;
    display: inline-block;
    width: 6.96rem;
    position: absolute;
    top: 2.84rem;
    left: .34rem;
    padding-bottom: 1.58rem;
    background: #f5f6f7;
}
.footer{
    width: 7.5rem;
    height: .88rem;
    position: fixed;
    bottom: 0;
    left: 0;
}
.footer .footer_left{
    width: 3.73rem;
    height: .88rem;
    float: left;
    border-right: 1px solid white;
    background-color: #489EEA;
    display:flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}
.footer .footer_right{
    width: 3.73rem;
    height: .88rem;
    float: left;
    background-color: #489EEA;
    text-align: center;
    display:flex;
    align-items: center;
    justify-content: center;

}
.footer .footer_left img{
    width:.356rem;
    height:.304rem;
    display: inline-block;
}
.footer .footer_left span{
    font-family: PingFangSC-Medium;
    display: inline-block;
    /* width: 1.28rem; */
    height:.44rem;
    line-height: .44rem;
    font-size: .32rem;
    color: #FFFFFF;
    margin-left: .2rem;
}
.footer .footer_right img{
    width:.356rem;
    height:.304rem;
    display: inline-block;
    margin-left:.2rem;
}
.footer .footer_right span{
    font-family: PingFangSC-Medium;
    display: inline-block;
    /* width: 1.28rem; */
    height:.44rem;
    line-height: .44rem;
    font-size: .32rem;
    color: #FFFFFF;
}
</style>

