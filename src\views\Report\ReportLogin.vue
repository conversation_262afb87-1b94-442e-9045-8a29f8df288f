<template>
  <div>
    <div class="wrap">
      <div class="login">
        <div class="hspLogo">
          <img src="../../assets/hspLogo.png" alt />
        </div>
        <div class="textinfo">
          <div style class="ReportLnc">
            <van-icon name="manager" size="32" color="#d1dde8" />
            <input type="text" placeholder="请输入姓名" maxlength="6" v-model="report_name" />
          </div>
          <div style class="ReportIdcard">
            <van-icon class="iconfont" class-prefix="icon" name="real_name" size="32" color="#d1dde8" />
            <input type="text" placeholder="请输入本人身份证件号" maxlength="18" v-model="report_idcard" />
          </div>
          <div style class="ReportTel">
            <van-icon name="phone-circle" size="32" color="#d1dde8" />
            <!-- <van-icon name="show_gongsiguanli_fill" size="32" color="#d1dde8"/> -->
            <input type="text" placeholder="请输入本人手机号码" maxlength="11" v-model="report_tel" />
          </div>

           <!-- 验证码 -->
           <div class="checkCode">
            <van-icon name="comment-circle" size="32" color="#d1dde8" />
            <input type="number" placeholder="请输入验证码" maxlength="6" v-model="yzm_code" />
            <van-button style="width: 83%" round type="info" size="small" :disabled="canYzm" @click="getYzm">{{ yzmNum
            }}</van-button>
          </div>


          <div style="margin-top: 0.3rem;">
            <van-button type="primary" size="large" color="linear-gradient(to right, #4bb0ff, #888bf2)"
              @click="btnLog">登录</van-button>
          </div>
        </div>
        <div class="bottomtel">
          如有疑问请于上班时间致电
          <br />
          <a :href="'tel:0371-56533900'">0371-56533900</a>
          <!-- | <a href="tel:02034858870">020-***********</a> -->
        </div>
      </div>
    </div>
    <!--遮罩层-->
    <van-overlay :show="show" v-show="show">
      <div class="vanoverBtn">
        <van-loading type="spinner" color="#1989fa">登录中...</van-loading>
      </div>
    </van-overlay>
  </div>
</template>
<script>
import { storage, dataUtils,ajax } from '../../common';
import apiUrls from '../../config/apiUrls';
import { Toast } from 'vant';
import Vue from "vue";
export default {
  data() {
    return {
      baseData: Vue.prototype.baseData,
      show: false,
      // report_name:"陈某某",
      // report_idcard:"******************",
      // report_tel:"13377694479",
      report_name: "",
      report_idcard: "",
      report_tel: "",

      yzm_code: "",
      canYzm: false,//是否禁用按钮
      timeWait: 60,//定时器等待秒数
      baseTime: 60,//初始值
      yzmNum: "获取验证码",
      hospInfo:{},
    }
  },
  created() {
     this.hospInfo = JSON.parse(storage.session.get("hospInfo"));
  },
  methods: {
    //获取验证码
    getYzm() {
      this.report_tel = this.report_tel.trim();
      if (dataUtils.isTel(this.report_tel) != true) {
        Toast(dataUtils.isTel(this.report_tel));
        return;
      }
      this.canYzm = true;
      ajax
        .post(apiUrls.SendVerifyCode, { tel: this.report_tel }, { nocrypt: true })
        .then((r) => {
          r = r.data;
          if (!r.success) {
            Toast(r.returnMsg);
            this.canYzm = false;
            return;
          }
          Toast("验证码发送成功，5分钟内有效。");
          this.yzmtime();
        })
        .catch((e) => {
          Toast("网络异常");
          this.canYzm = false;
        });
    },
    //验证码定时
    yzmtime() {
      let _that = this;
      if (this.timeWait <= 1) {
        this.timeWait = this.baseTime;
        this.yzmNum = "获取验证码";
        this.canYzm = false;
        return;
      } else {
        this.timeWait--;
        this.yzmNum = this.timeWait + " s";
      }
      setTimeout(function () {
        _that.yzmtime();
      }, 1000);
    },
    btnLog() {
      var that = this;
      if (this.report_idcard == "" || this.report_name == "" || this.report_tel == "") {
        Toast('您还未完整录入信息，请输入！');
        this.show = false;
        return;
      }
      if (dataUtils.isTel(this.report_tel) != true) {
        that.show = false;
        Toast(dataUtils.isTel(this.report_tel));
        return;
      }
      if (dataUtils.isCardID(this.report_idcard) != true) {
        that.show = false;
        Toast(dataUtils.isCardID(this.report_idcard));
        return;
      }


      var userInfo={
        idCard : this.report_idcard,
        name :this.report_name,
        tel : this.report_tel
      }
      storage.session.set("reportList", JSON.stringify(userInfo));

      // this.$router.push({path: '/ReportList'})
      if (this.yzm_code == "") {
        Toast("请输入验证码");
        return;
      }
      // if (this.yzm_code.length != 6) {
      //   Toast("验证码不正确");
      //   return;
      // }
      // this.show = true;
      var reportList = {
        tel: this.report_tel,
        code: this.yzm_code
      };
      ajax
        .post(apiUrls.CheckVerifyCode, reportList, { nocrypt: true })
        .then((r) => {
          r = r.data;
          if (!r.success) {
            this.show = false;
            Toast(r.returnMsg);
            return;
          }
     
          setTimeout(() => {
            this.$router.push({
              path: '/ReportList'
            })
            that.show = false;
          }, 3000)
        })
        .catch((e) => {
          this.show = false;
          Toast("网络异常！");
        });
    },
  }
}
</script>
<style lang="scss" scoped>
.van-overlay {
  display: flex;
  justify-content: center;
  align-items: center;
}

.vanoverBtn {
  display: flex;
  width: 50%;
  height: 1rem;
  background-color: #fdfdfd;
  justify-content: center;
  align-items: center;
  border-radius: 30px;
}

.login {
  position: absolute;
  width: 100%;
  height: 100%;
  background: url(../../assets/bottomBg.png) 0 bottom/100% no-repeat;
  background-color: #fff;
  padding-top: 0.75rem;
  box-sizing: border-box;
}

.login .hspLogo {
  text-align: center;
}

.login .hspLogo img {
  width: 1.92rem;
  height: 1.92rem;
  // display: inline;
}

.textinfo {
  width: 70%;
  height: 6rem;
  margin: 0 auto;
  padding-top: 0.3rem;
}

.textimg {
  display: flex;
  /* justify-content: center; */
  align-items: center;
  width: 20%;
  height: 1.1rem;
}

.textimg img {
  width: 59%;
  height: 0.6rem;
  padding-top: 0.23rem;
}

.ReportLnc {
  display: flex;
  width: 80%;
  height: 1rem;
  /* text-align: center; */
  /* margin: 0 auto; */
  justify-content: center;
  align-items: center;
  border-bottom: solid 1px #a9a0a0;
}

.ReportLnc input {
  width: 100%;
  height: 0.8rem;
  font-size: 14px;
  border: none;
  padding-top: 0.12rem;
  color: #999;
  -webkit-text-fill-color: #999;
}

.van-button--normal {
  width: 80%;
}

.bottomtel {
  width: 90%;
  font-size: 14px;
  margin: 0 auto;
  text-align: center;
  line-height: 0.5rem;
  color: #939393;
  margin-top: 0.7rem;
}

.ReportLnc {
  display: flex;
  height: 1rem;
  width: 100%;
  justify-content: center;
  align-items: center;
  border-bottom: 1px solid #bcb4b4;
}

.ReportLnc input {
  height: 0.5rem;
  width: 100%;
  margin-left: 15px;
  margin-top: 1px;
  border: none;
  font-size: 18px;
  color: #999;
  -webkit-text-fill-color: #999;
}

.ReportIdcard {
  display: flex;
  height: 1rem;
  width: 100%;
  justify-content: center;
  align-items: center;
  border-bottom: 1px solid #bcb4b4;
  margin-top: 0.25rem;
}

.ReportIdcard input {
  height: 0.5rem;
  width: 100%;
  margin-left: 15px;
  margin-top: 1px;
  border: none;
  font-size: 18px;
  color: #999;
  -webkit-text-fill-color: #999;
}

.ReportTel {
  display: flex;
  height: 1rem;
  width: 100%;
  justify-content: center;
  align-items: center;
  border-bottom: 1px solid #bcb4b4;
  margin-top: 0.25rem;
}

.ReportTel input {
  height: 0.5rem;
  width: 100%;
  margin-left: 15px;
  margin-top: 1px;
  border: none;
  font-size: 18px;
  color: #999;
  -webkit-text-fill-color: #999;
}

.van-button--large {
  border-radius: 0.32rem !important;
  font-size: 18px !important;
  letter-spacing: 11px;
}

//遮罩层
.van-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.checkCode {
  display: flex;
  height: 1rem;
  width: 100%;
  justify-content: center;
  align-items: center;
  border-bottom: 1px solid #bcb4b4;
  margin-top: 0.25rem;
}

.checkCode input {
  height: 0.5rem;
  width: 100%;
  margin-left: 15px;
  margin-right: 15px;
  margin-top: 1px;
  border: none;
  font-size: 0.32rem;
  color: #999;
  -webkit-text-fill-color: #999;
}</style>