<template>
  <div>
    <div id="wrap">
      <div class="success-page">
        <!-- head -->
        <div class="success-head">
          <!-- <img src="~/Content/img/chongzhichenggong.png" alt=""> -->
          <img src="../../assets/PaySuccess.png" alt />
          <h3>确定信息是否有误</h3>
          <!-- <div
            v-if="isUsePay==true"
            style="display: flex;justify-content: center;margin-top: 0.15rem;font-size: 12px;width: 90%;margin: auto;line-height: 20px;color: brown"
          >
            <van-count-down
              :time="time"
              format="已预订成功,请在mm 分 ss 秒 内完成支付，否则系统将自动取消本次交易"
              @finish="finish"
            />
          </div>-->
        </div>
        <!-- content -->
        <div class="success-content">
          <div class="list-wrap">
            <p>体 检 号:</p>
            <span>{{PayRecord.regno}}</span>
          </div>
          <div class="list-wrap">
            <p>体检用户:</p>
            <span>{{PayRecord.name}}</span>
          </div>
          <div class="list-wrap">
            <p>体检日期:</p>
            <span>{{PayRecord.createtime}}</span>
          </div>
          <div class="list-wrap">
            <p>体检套餐:</p>
            <span>{{PayRecord.clus_name}}</span>
          </div>
          <!-- <div class="list-wrap">
            <p>下单时间:</p>
            <span>{{PayRecord.reg_time}}</span>
          </div> -->
          <div class="list-wrap">
            <p>套餐金额:</p>
            <span>{{PayRecord.PayAmount}}</span>
          </div>
        </div>
        <!-- button -->
        <div class="footer-btn" v-if="comfimPay==true">
          <button @click="lookOrder()">取消支付</button>
          <button @click="PayAmoutOnlick()" :style="{'background-color':payDisabled?'#9c918d':''}">确定支付</button>
        </div>
        <div class="past" v-else>
          <span>订单已过期</span>
        </div>
        <!-- <div class="tip">
          <p v-if="isShowNote" style="width: 96%;margin: 0 auto;">预约成功后如需取消订单，请于体检前一天16:30分登录微信进行操作</p>
        </div>-->
      </div>
    </div>
  </div>
</template>
<script>
import { storage, ajax } from "../../common";
import { Toast } from "vant";
import apiUrls from "../../config/apiUrls";
import wx from "weixin-jsapi";
import Vue from "vue"

export default {
  data() {
    return {
      comfimPay: true,
      isShowNote: true,
      time: "",
      PayRecord: [],
      regno: "",
      payDisabled:true//是否禁止点击支付按钮
    };
  },
  created() {
    this.getList(); //通过体检号查询信息渲染页面
  },
  methods: {
    getList() {
      var pData = { regno: this.$route.query.regno }; //获取路由传参的体检号
      ajax
        .post(apiUrls.GetInforMation, pData, { nocrypt: true })
        .then(r => {
          if (r.data.success==false) {
            Toast(r.data.returnMsg);
            return;
          }
          
          let result=JSON.parse(r.data.returnData) || [];
          if(result.length==0 || result[0].hasOwnProperty("Info")){
            Toast("该体检人员不存在");
            return;
          }
          this.PayRecord = result[0];
          this.payDisabled=false;
          //console.log(this.PayRecord);
          //获取openid
          this.PayRecord.openid=storage.cookie.get("openid");
        })
        .catch(e => {
          Toast("系统异常！请联系工作人员");
          return;
        });
    },

    // openid:storage.cookie.get("openid"), 获取openid
    PayAmoutOnlick() {
      //禁止点击支付按钮
      if(this.payDisabled){
        return;
      }
      this.payDisabled=true;

      var that = this;
      
      that.PayRecord.price=that.PayRecord.PayAmount;//套餐价格赋值
      that.PayRecord.tj_date=that.PayRecord.createtime;//体检日期赋值

      var pData = that.PayRecord;
      ajax
        .post(apiUrls.SweepCodePay, pData, { nocrypt: true })
        .then(r => {
          if(!r.data.success){
            alert(r.data.returnMsg);
            return;
          };

          var data = r.data.returnData;
          WeixinJSBridge.invoke(
            "getBrandWCPayRequest",
            {
              appId: data.appid, //公众号名称，由商户传入
              timeStamp: data.timeStamp, //时间戳
              nonceStr: data.nonceStr, //随机串
              package: data.package, //扩展包
              signType: "MD5", //微信签名方式:MD5
              paySign: data.paySign //微信签名
            },
            function(res) {
              switch (res.err_msg) {
                case "get_brand_wcpay_request:cancel":
                  alert("取消支付");
                  that.$router.replace("/PayRecord");
                  break;
                case "get_brand_wcpay_request:fail":
                  alert(
                    "支付失败，可能的原因：签名错误、未注册APPID、项目设置APPID不正确、注册的APPID与设置的不匹配、其他异常等。"
                  );
                  break;
                case "get_brand_wcpay_request:ok":
                  alert("支付成功");
                  that.$router.replace("/PayRecord");
                  break;
              }
            }
          );

          //设置支付按钮可以点击
          this.payDisabled=false;
        })
        .catch(e => {
          //设置支付按钮可以点击
          this.payDisabled=false;
          alert("系统繁忙！请稍后再试");
          return;
        });
    },
    lookOrder() {
      this.$router.replace("/");
    } 
  }
};
</script>
<style lang="scss">
.past {
  width: 80%;
  background-color: #939aa2;
  text-align: center;
  margin: 0 auto;
  display: flex;
  height: 1rem;
  justify-content: center;
  align-items: center;
  border-radius: 0.2rem;
  margin-top: 0.3rem;
}
.past span {
  font-size: 22px;
  color: #fff;
}
.success-page {
  padding: 0.2rem 0;
  background: #f0eff6;
  height: 100%;
  width: 100%;
  overflow: auto;
}

.success-head {
  padding: 0.36rem 0 0.44rem 0;
}

.success-head img {
  width: 1.52rem;
  height: 1.52rem;
  display: block;
  margin: 0 auto 0.2rem;
}

.success-head h3 {
  text-align: center;
  font-size: 0.4rem;
  color: #354052;
  font-weight: normal;
}

.success-content {
  width: 96%;
  background: #fff;
  border-radius: 5px;
  padding: 0.4rem 0;
  margin-bottom: 0.48rem;
  margin: 0 auto;
}

.success-content .list-wrap {
  display: flex;
  height: 0.44rem;
  align-items: center;
  font-size: 0.32rem;
  color: #7f8fa4;
  overflow: hidden;
  margin-bottom: 0.24rem;
  padding-left: 0.28rem;
}

.success-content .list-wrap:last-child {
  margin-bottom: 0;
}

.success-content .list-wrap span {
  color: #354052;
  flex-grow: 1;
  /*text-align: right;*/
}
.footer-btn {
  display: flex;
  justify-content: space-around;
  margin-top: 0.5rem;
}
.footer-btn button {
  width: 40%;
  height: 1.08rem;
  border-radius: 5px;
  background: #80b1eb;
  border: 1px solid #80b1eb;
  display: block;
  font-size: 0.36rem;
  color: #ffffff;
}

.footer-btn button a {
  color: #ffffff;
}

.tip {
  /* margin-bottom: 1rem; */
  margin-top: 0.2rem;
  color: red;
  font-size: 0.3rem;
  justify-content: center;
}
</style>