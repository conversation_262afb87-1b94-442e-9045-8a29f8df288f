<template>
  <div>
    <div class="titletou">套餐加项（点击即可选择加项项目）</div>
    <div style="margin-top: 0.2rem;">
      <van-tree-select
        :items="clusItems"
        :active-id.sync="activeIds"
        :main-active-index.sync="activeIndex"
        :height="600"
        @click-item="right"
        @click-nav="leftclick"
      />
    </div>
    <div class="footOne">
      <div class="footLeft">
        <div class="LeftBtn">
          <span>￥{{price.toFixed(2)}}</span>
          <span></span>
        </div>
      </div>
      <div class="footMiddle">
        <span>已选{{activeIds.length}}项体检项目</span>
      </div>
      <div class="footRight" @click="nextclick">
        <span>立即预约</span>
      </div>
    </div>
    <div class="footDiv"></div>

    <van-popup v-model="showPop" round :style="{ 'max-height': '70%',width:'90%' }">
      <div class="showBox">
        <div class="showTitles">确认项目</div>
        <!-- 统计 -->
        <div class="total">
          <div class="totalProject">
            <div class="totalText1">项目</div>
            <div class="totalText2">{{confirmProj.sumNum}}个</div>
          </div>
          <div class="hr"></div>
          <div class="totalProject">
            <div class="totalText1">共计</div>
            <div class="totalText2">￥{{price.toFixed(2)}}</div>
          </div>
        </div>
        <!-- 列表 -->
        <div class="showLists" v-for="(item, index) in confirmProj.clusProj" :key="index">
          <div class="showTitle">{{item.title}}</div>
          <div class="showText" v-for="(projObj, indexs) in item.projArr" :key="indexs">
            <div class="showNo1">{{indexs+1}}、{{projObj.text}}</div>
            <div class="showNo2">{{(projObj.price || 0.00).toFixed(2)}}</div>
          </div>
        </div>
      </div>
      <div class="toBox">
        <div class="toGo" @click="immediately">返回修改</div>
        <div class="toGo toGreen" @click="Confirmationsheet">确认预约</div>
      </div>
    </van-popup>
  </div>
</template>
<script>
import { ajax, dataUtils, storage,toolsUtils } from "../../common";
import apiUrls from "../../config/apiUrls";
import {Toast} from 'vant'
export default {
  data() {
    return {
      time: "",
      times: false,
      //确认项目弹框
      confirmProj: {
        sumNum: 0,
        sumPrice: 0,
        clusProj: []
      },
      //初始数据后台返回
      items: [],
      activeIds: [],
      activeIndex: 0,
      imgs: "",
      showPop: false,
      clusItem: [],
      CombItemList: [],
      addItemCombData:[],
      price: 0.00,
      //已选套餐
      testItem: [
        // { clus_name: "口腔检查", clus_code: "j01", price: 225.05 },
        // { clus_name: "颈椎CT增强", clus_code: "c07", price: 325.0 },
        // { clus_name: "电子鼻内镜", clus_code: "c09", price: 345.0 },
        // { clus_name: "心脏彩超", clus_code: "d11", price: 415.0 }
      ],
      clusHideItem:[],//套餐隐藏项目数组
    };
  },
  created() {
     var clus_code = storage.session.get('clus_code');
     this.GetItemCombList(clus_code);
      toolsUtils.sleep(500);
     this.AddClusItem();
    // this.price = JSON.parse(storage.session.get("dataList")).price;
  },
  computed: {
    //计算每个项目分类的徽标数
    clusItems() {
      return this.clusItem.map(item => {
        var badgeNum = item.children.filter(i => this.activeIds.includes(i.id))
          .length;
        if (badgeNum > 0) {
          return { ...item, badge: badgeNum };
        }
        return item;
      });
    }
  },
  methods: {
     //获取套餐项目
        GetItemCombList(clus_Code){          
            var pData={
                comb_code:clus_Code
            }
            var that=this;
          ajax.post(apiUrls.GetItemCombList,pData,{nocrypt:true}).then(r=>{
              var data=r.data.returnData;
              if(r.data.success){
                    that.detailed=data;  
                     storage.session.set('clusIncludeItem',JSON.stringify(data));
                      that.testItem=JSON.parse(storage.session.get("clusIncludeItem")).map(i=>{
                        return {text:i.comb_Name,clus_code:i.comb_Code,price:i.comb_Price};
                      });
                      console.log(that.testItem);                 
              }
              else{
                  alert("暂无项目数据");
              }
          })
          .catch(e=>{
              alert("系统繁忙！请稍后再试");
          })
        },
    nextclick() {
      var sumNum = 0;
      var sumPrice = 0;
      var clusIn = []; //套餐内项目
      var clusOut = []; //套餐外项目
      for (var i = 0; i < this.items.length; i++) {
        for (var j = 0; j < this.items[i].children.length; j++) {
          //判断是否是选中的项目
          if (this.activeIds.includes(this.items[i].children[j].clus_code)) {
            sumNum++;
            sumPrice += this.items[i].children[j].price;
            //判断选中的项目是否在套餐里面
            if (
              this.testItem.some(
                item => item.clus_code == this.items[i].children[j].clus_code
              )
            ) {
              clusIn.push(this.items[i].children[j]);
            } else {
              clusOut.push(this.items[i].children[j]);
            }
          }
        }
      }

      //套餐内项目加上套餐隐藏项目
      clusIn=clusIn.concat(this.clusHideItem);
      //项目总数加上套餐隐藏项目数
      sumNum+=this.clusHideItem.length;

      //组合弹框数组
      this.confirmProj.sumNum = sumNum;
      this.confirmProj.sumPrice = sumPrice;

      this.confirmProj.clusProj = [];
      if (clusIn.length != 0) {
        this.confirmProj.clusProj.push({
          title: "套餐内项目（" + clusIn.length + ")",
          projArr: clusIn
        });
      }
      if (clusOut.length != 0) {
        this.confirmProj.clusProj.push({
          title: "套餐外项目（" + clusOut.length + ")",
          projArr: clusOut
        });
      }
      this.addItemCombData=this.confirmProj.clusProj[1].projArr.map(r=>r.clus_code);
      // console.log(this.addItemCombData);
      this.showPop = true;
    },
    immediately() {
      this.showPop = false;
    },
    Confirmationsheet() {
      storage.session.set("chooseItem",JSON.stringify({totalPrice:this.price.toFixed(2),chooseCombCode:this.addItemCombData.join(",")}));
      this.showPop = false;
      this.$router.push({
        path: "/TeamBookSum"
      });
    },
    leftclick(index) {},
    right(activeIds) {
      // debugger
      if (this.activeIds.includes(activeIds.id)) {
        // this.price += activeIds.price;
        this.price=dataUtils.priceSum(this.price,activeIds.price);
      } else {
        // this.price -= activeIds.price;
        this.price=dataUtils.priceSum(this.price,activeIds.price,"T");
      }
    },
    AddClusItem() {
      //获取所有分类项目数据
      ajax.post(apiUrls.GetAddClusItemList,{}).then(r => {
          r = r.data;
          if (!r.success) {
            Toast("加载数据失败！");
            return;
          }
          this.items = JSON.parse(r.returnData);

          //获取套餐内不显示的项目
          this.clusHideItem=this.testItem.filter(i=>{
            for(let j=0,len=this.items.length;j<len;j++){
              if(this.items[j].children.some(z=>(z.clus_code || "").trim()==(i.clus_code ||"").trim())){
                return false;
              }
            }
            return true;
          });

          var that = this;
          var item = [];
          for (var i = 0; i < that.items.length; i++) {
            for (var j = 0; j < that.items[i].children.length; j++) {
              for (var z = 0; z < that.testItem.length; z++) {
                if(that.testItem[z].clus_code == that.items[i].children[j].clus_code)
                {
                  that.items[i].children[j].disabled = true;
                }
              }
              //索引类型 clus_code
              that.items[i].children[j].id =
              that.items[i].children[j].clus_code;
            }
          }

          that.clusItem = that.items;

          //默认选中套餐包含的组合项目
          //that.activeIds=["j01","c06"];
          that.activeIds = that.testItem.map(item => item.clus_code);
        })
        .catch(err => {
          Toast("系统异常！");
        });
    }
  }
};
</script>
<style lang="scss" scoped>
.titletou {
  width: 100%;
  height: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #6a9be4;
  font-size: 16px;
  color: #ffffff;
}
.van-sidebar-item {
  line-height: 53px !important;
}
.van-tree-select__item {
  line-height: 54px !important;
  border-bottom: 1px solid #e9e3e3;
}
// .van-tree-select{
//     height: 0px !important;
// }
.treer {
  width: 100%;
  min-height: 500px;
  background: antiquewhite;
  margin-top: 0.2rem;
  display: flex;
}
.treerClus {
  background: #fafafa;
  width: 30%;
  font-size: 20px;
}
.treerItem {
  background: aqua;
  width: 70%;
  font-size: 20px;
}

.footOne {
  width: 100%;
  height: 1.16rem;
  position: fixed;
  left: 0;
  bottom: 0;
  display: flex;
  font-size: 0.24rem;
  background: white;
  border-top: 1px solid #dfe3e9;
}

.footOne .footLeft {
  width: 2.2rem;
  height: 96%;
  margin-left: 4%;
  color: #d0021b;
  letter-spacing: -0.02px;
  display: flex;
  align-items: center;
}

.footOne .footLeft .LeftBtn {
  width: 100%;
  height: 0.56rem;
  border-right: 2px solid #9b9b9b;
}

.footOne .footLeft .LeftBtn span:nth-child(2) {
  font-size: 0.4rem;
}

.footOne .footMiddle {
  width: 3.08rem;
  height: 100%;
  color: #9b9b9b;
  letter-spacing: -0.02px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.footOne .footRight {
  width: 2.22rem;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #6a9be4;
  font-size: 0.32rem;
  color: #ffffff;
  letter-spacing: -0.02px;
}
@mixin flex() {
  display: flex;
  justify-content: center;
  align-items: center;
}
.showBox {
  width: 90%;
  max-height: 60vh;
  margin-left: 5%;
  overflow-y: auto;
  color: #4a4a4a;
  .showTitles {
    width: 100%;
    height: 0.8rem;
    @include flex();
    font-size: 0.36rem;
    font-weight: 600;
    border-bottom: 1px solid #e5e5e5;
  }
  // 统计
  .total {
    width: 100%;
    height: 1.5rem;
    margin-top: 0.4rem;
    box-shadow: 3px 3px 7px #888888;
    border-radius: 5px;
    padding-bottom: 0.2rem;
    @include flex();
    font-size: 0.32rem;
    .totalProject {
      width: calc(50% - 0.5px);
      height: 100%;
      .totalText1 {
        width: 100%;
        height: 50%;
        @include flex();
        align-items: flex-end;
      }
      .totalText2 {
        width: 100%;
        height: 50%;
        @include flex();
        color: red;
      }
    }
    .hr {
      height: 70%;
      width: 1px;
      background: #dcdfe6;
    }
  }
  // 列表
  .showLists {
    width: 100%;
    font-size: 0.28rem;
    color: #4a4a4a !important;
    padding: 5px 0;
    box-shadow: 3px 3px 7px #888888;
    margin-top: 0.2rem;
    border-radius: 5px;
    @include flex();
    flex-direction: column;
    .showTitle {
      width: 100%;
      height: 0.8rem;
      @include flex();
      border-bottom: 1px solid #e5e5e5;
      font-weight: 600;
      font-size: 0.36rem;
    }
    .showText {
      width: 100%;
      display: flex;
      margin-top: 0.1rem;
      margin-bottom: 0.1rem;
      .showNo1 {
        width: 80%;
        min-height: 0.6rem;
        @include flex();
        justify-content: flex-start;
        padding-left: 0.2rem;
      }
      .showNo2 {
        width: 20%;
        min-height: 0.6rem;
        @include flex();
        justify-content: flex-start;
        padding-left: 0.2rem;
        color: red;
      }
    }
  }
}
.toBox {
  width: 100%;
  height: 1.4rem;
  display: flex;
  justify-content: space-around;
  align-items: center;
  .toGo {
    width: 40%;
    height: 0.8rem;
    color: #4a4a4a;
    border-radius: 5px;
    @include flex();
    font-size: 0.36rem;
    background: white;
    border: 1px solid #e5e5e5;
    letter-spacing: 1px;
  }
  .toGreen {
    background: #409eff !important;
    color: white;
  }
}
</style>
