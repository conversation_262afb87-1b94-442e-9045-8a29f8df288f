<template>
    <div id="all">
        <div id="calendar">
            <div class="month">
                <div class="prevMonth" @click="prevMonth" v-show="pre">
                    上一月
                </div>
                <div class="prevMonth_" v-show="pre_"></div>
                <div class="year-month">
                    <span class="choose-year">{{ currentDateStr }}</span>
                </div>
                <div class="nextMonth" @click="nextMonth">
                    下一月
                </div>
            </div>
            <div class="weekdays">
                <div class="week-item" v-for="item of weekList" :key="item">{{ item }}</div>
            </div>
            <div class="calendar-inner">
                <div class="calendar-item" v-for="(item, index) of calendarList" :key="index"
                    v-bind:class="[item.disable ? 'disabled' : '']">
                    <div @click="item.Thing === '' ? '' : (matchDate(item.ThingName) ? dayClick(item.date, item.ThingName, index) : '')"
                        :class="ClassKey === calendarList[index].value ? 'chooseDay' : ''">
                        <div class="calendarDate " v-bind:class="switchHaoyuanClass(item.ThingName)">{{ item.date }}</div>
                        <div class="calendarThing" v-bind:class="switchHaoyuanClass(item.ThingName)">{{ item.Thing }}</div>
                    </div>
                </div>
            </div>

        </div>

        <!-- 套餐 -->
        <div class="sumtime" v-show="stateShow">
            <div :class="items.team_Surplus === 0 ? 'timeTwo' : 'timese'" v-for="(items, index) in sumtimeList" :key="index"
                @click="timeBtn(items, index)">
                <span :class="TeamSpans === index ? 'TeamSpan' : 'TeamSpanTwo'">
                    {{ items.sumtime_Name }}
                </span>
            </div>
        </div>
        <div class="PersonalCard top">
            <div class="CardTitle">
                <div class="titleImg">
                    <img src="../../assets/detailsSex01.png" alt="套餐详情">
                </div>

                <div class="CardText"><span>{{ clus_name }}</span></div>

                <div class="CardSee" @click="toTeamDetails">
                    <span>查看详情</span>
                    <span style="margin-top: 4.1px;"><img src="../../assets/Nextpage.png" alt="查看详情"></span>
                    <!-- <div class="CardSeeImg"><img src="./picture/Nextpage.png" alt="查看详情"></div> -->
                </div>
            </div>
            <!-- <div class="PersonalSpan">
                <span>{{item.PersonalSpan}}</span>
            </div> -->
        </div>
        <div class="teamDiv">
            <div class="Teamconfirm" @click="TeamOver">立即预约</div>
        </div>
    </div>
</template>
<script>
import apiUrls from '../../config/apiUrls'
import { ajax, storage } from '../../common'
import { Toast } from 'vant';
import Vue from "vue";

export default {
    data() {
        return {
            stateShow: false,
            ClassKey: "", //点击选择日期
            years: "", //年
            months: "", //月
            pre: false, //上一月
            pre_: true, //上一月代替
            week: 0, //周几
            current: {}, //当前时间
            calendarList: [], //用于遍历显示
            shareDate: new Date(), //享元模式，用来做优化的,
            weekList: ["日", "一", "二", "三", "四", "五", "六"], // 新增
            CardData: [{
                CardText: "入职套餐（不限性别）",
            },],
            sumtimeList: [],//时段数据
            TeamSpans: "",//样式判断
            sumtime_Code: "",//时段编码
            sumtime_Name: "",//时段名称
            LncUserinfo: [],//单位用户信息
            clus_name: "",
            spType: "",//是否公用号源
             hospInfo:{},
        }
    },
    computed: {
        // 显示当前时间
        currentDateStr() {
            let {
                year,
                month
            } = this.current;
            return `${year}-${this.pad(month + 1)}`;
        }
    },
    mounted() {
        this.init();
    },
       created() {
        this.hospInfo=JSON.parse(storage.session.get("hospInfo"));
    },
    methods: {
        //时间段选择判断
        timeBtn(items, index) {
            var that = this;
            if (items.team_Surplus <= 0) {
                Toast("该时段已无号源！请选择其他时段");
                return;
            }
            that.TeamSpans = index;
            that.sumtime_Name = items.sumtime_Name;
            that.sumtime_Code = items.sumtime_Code;
        },
        init() {

            this.LncUserinfo = JSON.parse(storage.session.get("teamInfo"));
            this.clus_name = this.LncUserinfo[0].clus_name;
            // 初始化当前时间
            this.setCurrent();
            this.calendarCreator();
            // this.switchHaoyuanClass();
        },
        // 判断当前月有多少天
        getDaysByMonth: function (year, month) {
            return new Date(year, month + 1, 0).getDate();
        },
        getFirstDayByMonths: function (year, month) {
            return new Date(year, month, 1).getDay();
        },
        getLastDayByMonth: function (year, month) {
            return new Date(year, month + 1, 0).getDay();
        },
        // 对小于 10 的数字，前面补 0
        pad: function (str) {
            return str < 10 ? `0${str}` : str;
        },
        // 点击上一月
        prevMonth: function () {
            this.current.month--;

            // 因为 month的变化 会超出 0-11 的范围， 所以需要重新计算
            this.correctCurrent();
            // 生成新日期
            this.calendarCreator();
        },
        // 点击下一月
        nextMonth: function () {
            this.current.month++;
            // 因为 month的变化 会超出 0-11 的范围， 所以需要重新计算
            this.correctCurrent();
            // 生成新日期
            this.calendarCreator();
        },
        // 格式化时间，与主逻辑无关
        stringify: function (year, month, date) {
            let str = [year, this.pad(month + 1), this.pad(date)].join("-");
            return str;
        },
        // 设置或初始化 current
        setCurrent: function (d = new Date()) {
            let year = d.getFullYear();
            let month = d.getMonth();
            let date = d.getDate();
            this.current = {
                year,
                month,
                date
            };
        },
        // 修正 current
        correctCurrent: function () {
            let {
                year,
                month,
                date
            } = this.current;

            let maxDate = this.getDaysByMonth(year, month);
            // 预防其他月跳转到2月，2月最多只有29天，没有30-31
            date = Math.min(maxDate, date);

            let instance = new Date(year, month, date);
            this.setCurrent(instance);
        },
        // 生成日期
        calendarCreator: function () {
            // 一天有多少毫秒
            const oneDayMS = 24 * 60 * 60 * 1000;

            let list = [];
            let {
                year,
                month
            } = this.current;

            // 当前月份第一天是星期几, 0-6
            let firstDay = this.getFirstDayByMonths(year, month);
            // 填充多少天 firstDay-1则为周一开始，
            let prefixDaysLen = firstDay === 0 ? 0 : firstDay;
            // 毫秒数
            let begin = new Date(year, month, 1).getTime() - oneDayMS * prefixDaysLen;

            // 当前月份最后一天是星期几, 0-6
            let lastDay = this.getLastDayByMonth(year, month);
            // 填充多少天， 和星期的排放顺序有关
            let suffixDaysLen = lastDay === 0 ? 0 : 6 - lastDay;
            // 毫秒数
            let end =
                new Date(year, month + 1, 0).getTime() + oneDayMS * suffixDaysLen;

            while (begin <= end) {
                // 享元模式，避免重复 new Date
                this.shareDate.setTime(begin);
                let year = this.shareDate.getFullYear();
                let curMonth = this.shareDate.getMonth();
                let date = this.shareDate.getDate();
                let week = this.shareDate.getDay(); // 当前周几
                list.push({
                    year: year,
                    month: curMonth,
                    date: date,
                    Thing: "待开",
                    week: week,
                    ThingName: "",
                    disable: curMonth !== month,
                    value: this.stringify(year, curMonth, date)
                });

                begin += oneDayMS;
            }
            this.calendarList = list;
            this.judgeHaoyuan();
        },

        // 号源显示
        judgeHaoyuan: function () {
            var that = this;
            // 当前时间
            var nowDate = this.stringify(
                new Date().getFullYear(),
                new Date().getMonth(),
                new Date().getDate()
            );

            var pData = {
                lnccode: this.LncUserinfo[0].lnc_code,
                start: this.baseData.TeamsumStartTiem,
                end: this.baseData.TeamsumendTime,
                 hospCode:this.hospInfo.hospCode
            };
            ajax.post(apiUrls.GetTeamSumList, pData, { nocrypt: true }).then(r => {
                var TeamList = r.data.returnData;
                for (var i = 0; i < that.calendarList.length; i++) {
                    that.prevMonthIconShow();
                    if (nowDate > that.calendarList[i].value) {
                        that.calendarList[i].Thing = "";
                        continue;
                    }
                    for (var j = 0; j < TeamList.length; j++) {
                        //判断是否公用号源
                        if (TeamList[j].spType && TeamList[j].spType == "total") {
                            this.spType = "total";
                        }
                        if (that.calendarList[i].value == TeamList[j].team_Date) {
                            if (TeamList[j].team_Flag == "T") {
                                that.calendarList[i].Thing = "休假";
                                that.calendarList[i].ThingName = "休假";
                                break;
                            }
                            var team_Surplus = TeamList[j].team_Surplus;
                            if (TeamList[j].team_Sum <= 0) {
                                that.calendarList[i].Thing = "待开";
                                that.calendarList[i].ThingName = "待开";
                                break;
                            }
                            if (team_Surplus > 10) {
                                that.calendarList[i].Thing = "余" + team_Surplus + "人";
                                that.calendarList[i].ThingName = "充足";
                            }
                            else if (team_Surplus == 0) {
                                that.calendarList[i].Thing = "约满";
                                that.calendarList[i].ThingName = "约满";
                            }
                            else if (team_Surplus <= 10) {
                                that.calendarList[i].Thing = "余" + team_Surplus + "人";
                                that.calendarList[i].ThingName = "紧张";
                            }
                        }
                    }
                }
            })
                .catch(e => {
                    alert("服务异常！请稍等");
                    return;
                })

        },
        // 根据日期显示添加类名
        switchHaoyuanClass: function (value) {
            switch (value) {
                case "紧张":
                    return "haoyuan-green";
                    break;
                case "约满":
                    return "haoyuan-red";
                    break;
                case "休假":
                    return "haoyuan-red";
                    break;
                case "充足":
                    return "haoyuan-adequate";
                    break;
                case "":
                    return "";
                    break;
            }
        },

        // 号源匹配触发点击
        matchDate: function (date) {
            if (date == "充足" || date == "紧张") {
                return true;
            } else if (date === "约满" || "待开" || "") {
                return false;
            }
        },

        // 当前年月之前不能跳转回去
        prevMonthIconShow: function () {
            if (this.current.year == new Date().getFullYear()) {
                if (this.current.month > new Date().getMonth()) {
                    this.pre = true;
                    this.pre_ = false;
                } else {
                    this.pre = false;
                    this.pre_ = true;
                }
            } else if (this.current.year > new Date().getFullYear()) {
                this.pre = true;
                this.pre_ = false;
            }
        },
        // 日期点击事件
        dayClick: function (date, key, index) {
            // 判断能点击的日期是否为号源充足和紧张,休假，待开，""都不可点击
            if (key == "充足" || key == "紧张") {
                this.years = this.current.year;
                this.months = this.current.month + 1;
                this.ClassKey =
                    this.years +
                    "-" +
                    (this.months < 10 ? "0" + this.months : this.months) +
                    "-" +
                    (date < 10 ? "0" + date : date);
                this.week = this.calendarList[index].week;
                if (this.spType == "total") {
                    var pData = {
                        date_Time: this.ClassKey,
                        lnccode: "total",
                    }
                }
                else {
                    var pData = {
                        date_Time: this.ClassKey,
                        lnccode: this.LncUserinfo[0].lnc_code
                    }
                }
                //不需要号源时段的项目注释掉
                this.TeamSpans = "";
                var that = this;
                ajax.post(apiUrls.GetTeamSumTimeList, pData, { nocrypt: true }).then(r => {
                    if (r.data.success) {
                        that.stateShow = true;
                        var List = r.data.returnData;
                        that.sumtimeList = List;
                    }
                }).catch(e => {
                    Toast('系统异常！请联系管理员');
                    return;
                })
                //
                return true;
            } else {
                this.ClassKey = false;
                return false;
            }
        },

        // 跳转
        TeamOver: function () {
            if (this.ClassKey == '' && this.ClassKey == false) {
                Toast('请选择体检日期');
                return;
            }
            else {
                if (!this.sumtime_Code) {
                    Toast('请选择报道时间');
                    return;
                }
                var TeamDay = {
                    date: this.ClassKey,
                    week: this.week,
                    clus_name: this.LncUserinfo[0].clus_name,
                    clus_code: this.LncUserinfo[0].clus_code,
                    name: this.LncUserinfo[0].pat_name,
                    idcard: this.LncUserinfo[0].idcard,
                    lnccode: this.LncUserinfo[0].lnc_code,
                    tel: this.LncUserinfo[0].tel,
                    LncName: this.LncUserinfo[0].lnc_name,
                    sumtime_Code: this.sumtime_Code,
                    sumtime_Name: this.sumtime_Name,
                    reg_no: this.LncUserinfo[0].reg_no,
                    spType: this.spType
                }
                storage.session.set('TeamDay', JSON.stringify(TeamDay));
                this.$router.push({
                    path: '/TeamOrders'
                })
            }
        },
        // 套餐展示
        toTeamDetails: function () {
            var cde = {
                cluscode: this.LncUserinfo[0].clus_code,
                clusname: this.LncUserinfo[0].clus_name
            }
            sessionStorage.setItem("cde", JSON.stringify(cde));
            this.$router.push({
                path: '/TeamDetails'
            })
        },
    }
}
</script>
<style lang="scss" scoped>
#all {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
}

#calendar {
    width: 100%;
    height: 7.36rem;
    position: relative;
    top: 0;
    left: 0;
    background: white;
    /* -moz-box-shadow: 2px 2px 5px #333333;
            -webkit-box-shadow: 2px 2px 5px #333333;
            box-shadow: 2px 2px 5px #333333; */
}

.month {
    height: 0.88rem;
    line-height: 0.88rem;
    text-align: center;
    font-size: 0.28rem;
    color: #4A4A4A;
    background-color: #fff;
    margin: 0;
    padding: 0;
    display: flex;
    justify-content: space-between;
    letter-spacing: -0.01px;
}

.prevMonth {
    width: 1.2rem;
    height: 0.88rem;
    line-height: 0.88rem;
    margin-left: 0.2rem;
    color: #6A9BE4;
}

.prevMonth_ {
    width: 1.2rem;
    height: 0.88rem;
    margin-left: 0.2rem;
}

.nextMonth {
    width: 1.2rem;
    height: 0.8rem;
    line-height: 0.88rem;
    margin-right: 0.2rem;
    color: #6A9BE4;
}

.weekdays {
    height: 0.48rem;
    line-height: 0.48rem;
    margin: 0;
    padding: 0 2%;
    display: flex;
    flex-wrap: wrap;
    color: #9B9B9B;
    font-size: .28rem;
    justify-content: space-around;
}

.weekdays .week-item {
    display: inline-block;
    width: 13.6%;
    text-align: center;
    font-size: 0.28rem;
}

.calendar-inner {
    margin-left: 0.2rem;
}

.calendar-item {
    box-sizing: border-box;
    float: left;
    width: 1.01rem;
    height: 1rem;
    text-align: center;
}

.calendarDate {
    width: 1.01rem;
    height: 0.5rem;
    font-size: 0.4rem;
    color: #ccc;
    line-height: 0.65rem;
    text-align: center;
}

.calendarThing {
    width: 1rem;
    font-size: 0.22rem;
    height: 0.5rem;
    text-align: center;
    color: #ccc;
}

.calendar-item.disabled {
    visibility: hidden;
}

.calendar-item.checked {
    color: red;
}

.chooseDay {
    border-radius: 50%;
    background-color: #018bf0;
}

.chooseDay div {
    color: #fff !important;
}

.haoyuan-red {
    color: #D0021B;
}

.haoyuan-gray {
    color: #ccc;
}

.haoyuan-green {
    color: #ff9800;
}

.haoyuan-adequate {
    color: #1BA05F;
}

/* 跳转按钮 */
.teamDiv {
    width: 100%;
    height: 1.68rem;
    position: fixed;
    bottom: 0;
    background: white;
    display: flex;
    justify-content: center;
    align-items: center;
}

.Teamconfirm {
    width: 92%;
    height: .96rem;
    background: #6A9BE4;
    border-radius: 5px;
    font-size: .32rem;
    color: #FFFFFF;
    letter-spacing: -0.02px;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* 套餐 */
.PersonalCard {
    width: 94%;
    height: 1.1rem;
    // margin: .28rem auto;
    margin-bottom: 2.3rem;
    /* position: relative;
            top: 7.36rem; */
    background: white;
    font-size: .28rem;
    border-radius: .1rem;
    -moz-box-shadow: 2px 2px 5px #333333;
    -webkit-box-shadow: 2px 2px 5px #333333;
    box-shadow: 2px 2px 5px #333333;
}

/* .top {
            margin-top: .24rem;
        } */

.PersonalCard .CardTitle {
    width: 96%;
    height: 1.1rem;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 4%;
}

.CardTitle .titleImg {
    width: .56rem;
    height: .56rem;
    display: flex;
    justify-content: center;
    align-items: center;
}

.CardText {
    width: calc(100% - 2.52rem);
    height: 1rem;
    display: flex;
    align-items: center;
    /* margin-left: .2rem; */
    font-size: .32rem;
    color: #4A4A4A;
    letter-spacing: -0.02px;
    font-weight: 600;
}

.CardText span {
    margin-left: .2rem;
}

.CardSee {
    width: 1.96rem;
    height: 1rem;
    color: #6A9BE4;
    letter-spacing: -0.01px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.CardSeeImg {
    width: .64rem;
    height: .64rem;
}

.icon-Nextpage {
    margin-top: 3px;
    font-size: .5rem;
}

.PersonalCard .PersonalSpan {
    width: 92%;
    height: 1.38rem;
    color: #9B9B9B;
    letter-spacing: -0.01px;
    line-height: .44rem;
    margin: 0 auto;
    /* 超过3行省略号 */
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}



/* 消息提示 */
.text-tip {
    display: block;
    background: rgba(0, 0, 0, .7);
    color: #fff;
    padding: 15px 15px;
    line-height: 18px;
    position: fixed;
    left: 50%;
    bottom: 55%;
    -webkit-transform: translate(-50%);
    transform: translate(-50%);
    border-radius: 3px;
    display: none;
    z-index: 9999;
    font-size: 14px;
    text-align: center;
}

/*时段样式*/
.sumtime {
    width: 94%;
    height: 1.5rem;
    margin: .28rem auto;
    background: #F5F5F5;
    font-size: .34rem;
    border-radius: .1rem;
    -webkit-box-shadow: 2px 2px 5px #333333;
    box-shadow: 2px 2px 5px #333333;
    display: flex;
    justify-content: space-evenly;
    -webkit-box-align: center;
    align-items: center;
}

.timese {
    width: 44%;
    height: 0.94rem;
    background: #ffffff;
    box-shadow: 0 3px 5px 0 rgba(153, 153, 153, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 6px;
}

.timeTwo {
    width: 44%;
    height: 0.94rem;
    background: #b0acac;
    box-shadow: 0 3px 5px 0 rgba(153, 153, 153, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 6px;
    color: #d1d0cf;
}

.TeamSpan {
    width: 100%;
    height: 100%;
    /* text-align: center; */
    display: flex;
    justify-content: center;
    align-items: center;
    background: #018bf0;
    border-radius: 5%;
    color: aliceblue;
}


.TeamSpanTwo {
    width: 100%;
    height: 100%;
    /* text-align: center; */
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 5%;
}
</style>