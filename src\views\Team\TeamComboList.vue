<!--
 * @Author: Reaper
 * @Date: 2025-03-17 17:12:29
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2025-05-08 17:23:35
 * @Description: 请填写简介
-->
<template>
  <div class="TeamComboList">
    <div v-for="(item, index) in orderList" key="index" class="order_item shadow">
      <p>
        <span class="left-span">姓名：</span><span class="right-span">{{ item.pat_name }}</span>
      </p>
      <p>
        <span class="left-span">身份证：</span><span class="right-span">{{ item.idcard }}</span>
      </p>
      <p>
        <span class="left-span">电话：</span><span class="right-span">{{ item.tel }}</span>
      </p>
      <p>
        <span class="left-span">套餐类型：</span><span style="color: blue;" class="right-span"> {{ item.flag }}</span>
      </p>
      <!-- <div>
        <van-button type="info" block @click="nextTick(item)"
          >下一步</van-button
        >
      </div> -->

      <van-cell :title="item.flag" is-link value="去预约"  @click="nextTick(item)"/>
    </div>
  </div>
</template>

<script>
import { ajax, storage, dataUtils } from "../../common";
import apiUrls from "../../config/apiUrls";
import { Toast, Dialog } from "vant";
import Vue from "vue";
export default {
  data() {
    return {
      orderList: [],
    };
  },
  created() {
    console.log(this.$route.query.orderList + "--------------");
    this.orderList = JSON.parse(this.$route.query.orderList);
  },
  methods: {
    nextTick(item) {
      console.log(item);
      storage.session.set("clus_code", item.clus_code.trim());
      storage.session.set("teamInfo", JSON.stringify([item]));
      storage.session.set(
        "lncInfo",
        JSON.stringify({ lnc_code: item.lnc_code, lnc_name: item.lnc_name })
      );
      this.GetClusItemDetail(item.clus_code.trim())

      this.ToCalendar(item)

    },
    GetClusItemDetail(comb_code) {
      var that = this;
      var pData = {
        comb_code: comb_code
      }
      ajax.post(apiUrls.GetItemCombList, pData, { nocrypt: true }).then(r => {
        if (r.data.success) {
          var data = r.data.returnData;


          storage.session.set("clusIncludeItem", JSON.stringify(data))
        } else {
          alert(r.data.returnMsg);
        }
      }).catch(e => {
        alert("系统繁忙！请稍后再试");
      })
    },

    ToCalendar(item) {
      storage.session.set("clusIncludeItem", this.clusIncludeItem);
      storage.session.set("selectTeamInfo", JSON.stringify(item));
      Dialog.confirm({
        title: '提示',
        message: "如有需求，可添加套餐外的体检项目。是否添加体检项目？",
        confirmButtonText: '是',
        cancelButtonText: '否'
      })
        .then(() => {
          this.$router.push({
            path: '/TeamAddClusItemToMe'
          });
          return;
        })
        .catch(() => {
          if(item.flag === "普通体检"){
            this.$router.push({
            path: '/HealthQuestionnaire',
            query:{
              regNo:item.reg_no
            }
          })
          return;
          }

          this.$router.push({
            path: '/OccupationalDiseasesQs',
            query:{
              regNo:item.reg_no
            }
          })
          
         
        });

    }
  },
};
</script>

<style scoped>
.left-span{
  width: 2rem;
  text-align: justify!important;
  font-weight: bold;
}
.right-span{

}
.TeamComboList{
  height: auto;
}
.order_item {
 
  width: 96vw;
  background-color: #f2f2f2;
  margin: .4rem auto;
}



.shadow {

  background-color: #ffffff;
  transition: box-shadow 500ms, transform 0.5s ease-in-out;
}


.order_item>p {

  margin: 0;
  padding: 0;
  height: .7rem;
  line-height: .7rem;
  font-size: .3rem;
  text-indent: .4rem;
}

.order_item>p>span {
 
}


</style>
