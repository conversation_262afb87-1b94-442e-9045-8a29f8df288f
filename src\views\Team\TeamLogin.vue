<template>
  <div>
    <div class="wrap">
      <div class="login">
        <div class="hspLogo">
          <img src="../../assets/hspLogo.png" alt />
        </div>
        <div class="textinfo">
          <div style class="TeamLnc">
            <van-icon class="iconfont" class-prefix="icon" name="show_gongsiguanli_fill" size="32" color="#d1dde8" />
            <!--单位下拉模糊查询  -->

            <div class="TeamLncTitle">
              <ul class="TeamUl">
                <li class="Teamli">
                  <div class="teamHidden">
                    <div class="TeamList">
                      <input type="text" placeholder="请输入单位" v-model="lnc_name" @input="getTjList" />
                    </div>
                    <div class="showList" v-if="Onhideen">
                      <div v-for="(item, index) in itemList" :key="index" @click="showLists(item)">{{ item.lnc_Name }}</div>
                    </div>
                  </div>
                </li>
              </ul>
            </div>
          </div>
          <div style class="TeamIdcard">
            <van-icon class="iconfont" class-prefix="icon" name="real_name" size="32" color="#d1dde8" />
            <input type="text" placeholder="请输入本人身份证件号" v-model="idCard" />
          </div>
          <div style class="TeamTel">
            <van-icon name="phone-circle" size="32" color="#d1dde8" />
            <!-- <van-icon name="show_gongsiguanli_fill" size="32" color="#d1dde8"/> -->
            <input type="text" placeholder="请输入本人手机号码" v-model="tel" />
          </div>
          <div style="margin-top: 0.3rem;">
            <van-button type="primary" size="large" color="linear-gradient(to right, #4bb0ff, #888bf2)"
              @click="btnLog">登录</van-button>
          </div>
        </div>
        <div class="bottomtel">
          如有疑问请于上班时间致电
          <br />
          <a :href="'tel:' + baseData.hospitalTel">{{ baseData.hospitalTel }}</a>
          <!-- | <a href="tel:02034858870">020-***********</a> -->
        </div>
      </div>
    </div>
    <van-dialog v-model="show_wj" show-cancel-button cancelButtonText="否" confirmButtonText="是"
      :beforeClose="beforeClose_wj">
      <div style="margin: 0.5rem .5rem;text-indent:2em ;">若您需要评估自身健康情况，可前往健康问卷自测，是否前往？</div>
    </van-dialog>
    <!--遮罩层-->
    <van-overlay :show="show" v-show="show">
      <div class="vanoverBtn">
        <van-loading type="spinner" color="#1989fa">登录中...</van-loading>
      </div>
    </van-overlay>
  </div>
</template>
<script>
import { ajax, storage, dataUtils } from "../../common";
import apiUrls from "../../config/apiUrls";
import { Toast } from "vant";
import Vue from "vue";
export default {
  data() {
    return {
      baseData: Vue.prototype.baseData,
      show: false,
      show_wj: false,
      idCard: '',
      tel: '',
      lnc_name: "",
      lnc_code: "",
      Onhideen: false, //搜索功能下拉框显示初始值
      unitList: [],
      itemList: [],
      orderList:[],
      //判断此版本是否需要加项功能
      IsAddClusItem: Vue.prototype.baseData.IsAddClusItem || false
    };
  },
  created() {
    // this.lnc_code="9991"
    // this.lnc_name='测试单位'
    // this.idCard=""
    // this.tel=""
    this.getUnitList();
  },
  methods: {
    //获取所有单位
    getUnitList() {
      let pada = {
        kw: "GetTjLncList001"
      }
      ajax
        .post(apiUrls.FindByTjLncList, pada, { nocrypt: true })
        .then(r => {
          this.unitList = r.data.returnData;
        })
        .catch(e => {
          alert('系统繁忙！请稍后再试');
          return;
        });
    },
    //单位模糊搜索点击获取单位数据
    showLists(item) {
      this.lnc_code = item.lnc_Code;
      this.lnc_name = item.lnc_Name;
      this.Onhideen = false;
    },
    //获取所有单位数据模糊查询单位数据
    getTjList() {
      var that = this;
      //没值的时候清空下拉框
      if (that.lnc_name.trim() == "") {
        that.Onhideen = false;
        return;
      }
      // if(!(that.unitList instanceof Array)){
      //     return;
      // }
      var len = that.unitList.length;
      var reg = new RegExp(that.lnc_name);
      var arr = [];
      for (var i = 0; i < len; i++) {
        var t = that.unitList[i].lnc_Name.match(reg);
        if (t) {
          arr.push(that.unitList[i]);
        }
      }
      that.itemList = arr;
      that.Onhideen = true;
    },
    btnLog() {
      var that = this;
      if (that.lnc_code == "" || that.idCard == "" || that.tel == "") {
        Toast("请输入完整信息");
        return;
      }
      var pData = {
        lnc_Code: that.lnc_code,
        idCard: that.idCard,
        tel: that.tel,
      }
      that.show = true;
      ajax.post(apiUrls.TeamLogin, pData, { nocrypt: true }).then(r => {
        var data = JSON.parse(r.data.returnData);
        if (r.data.success) {
          //提示存储过程返回的错误
          if (r.data.returnData.indexOf("err") != -1) {
            let errArr = JSON.parse(r.data.returnData);
            that.show = false;
            Toast(errArr[0].err);
            return;
          }
          console.log(r.data.returnData);
          this.orderList=r.data.returnData

          // storage.session.set("clus_code", data[0].clus_code.trim());
          // storage.session.set("teamInfo", JSON.stringify(data));
          // storage.session.set("lncInfo", JSON.stringify({ lnc_code: that.lnc_code, lnc_name: that.lnc_name }));
          that.show = false;
          this.show_wj =true;

          this.$router.push({
            path:"/TeamComboList",
            query:{
              orderList:r.data.returnData
            }
          })
          // setTimeout(() => {
          //   that.$router.push({
          //     path: "/TeamBookSum"
          //   });
          //   that.show = false;
          // }, 2000);
          return;
          //  if (!this.IsAddClusItem) {
          //     setTimeout(() => {
          //       that.$router.push({
          //         path: "/TeamBookSum"
          //       });
          //       that.show = false;
          //     }, 2000);
          //     return;
          //   }
          //   setTimeout(() => {
          //     that.$router.push({
          //       path: "/TeamAddClusItem"
          //     });
          //     that.show = false;
          //   }, 2000);
        } else {
          that.show = false;
          Toast(r.data.returnMsg);
        }
      });
    },
    beforeClose_wj(action, done) {
      if (action === 'confirm') {
        this.$router.push({
          path: "/QuestionIndex",
          query: {
            page: "group"
          }
        });
        done()
        return;
      }
      this.$router.push({
        path: "/TeamBookSum"
      });
      this.show_wj = false;
      done();
    },
  }
};
</script>
<style lang="scss">
.van-overlay {
  display: flex;
  justify-content: center;
  align-items: center;
}

.vanoverBtn {
  display: flex;
  width: 50%;
  height: 1rem;
  background-color: #fdfdfd;
  justify-content: center;
  align-items: center;
  border-radius: 30px;
}

.TeamLncTitle {
  height: 100%;
  width: 100%;
}

.TeamUl {
  height: 100%;
}

.Teamli {
  height: 100%;
  padding: 0 !important;
}

.teamHidden {
  height: 100%;
}

.TeamList {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.ClassShow {
  padding: 0 !important;
}

.testList {
  width: 100%;
  // height: .88rem;
}

.showList {
  width: 64%;
  max-height: 4.5rem;
  background: #eaeef1;
  font-size: 0.28rem;
  color: #43acf9;
  position: absolute;
  z-index: 999;
  border-radius: 0.1rem;
  word-wrap: break-word;
  overflow-y: scroll;
  box-shadow: 3px 3px 7px #888888;
}

.showList div {
  border-bottom: 1px solid white;
  padding-left: 0.1rem;
  min-height: 0.8rem;
  display: flex;
  /* justify-content: center; */
  align-items: center;
  font-size: 15px;
}

.login {
  position: absolute;
  width: 100%;
  height: 100%;
  background: url(../../assets/bottomBg.png) 0 bottom/100% no-repeat;
  background-color: #fff;
  padding-top: 0.75rem;
  box-sizing: border-box;
}

.login .hspLogo {
  text-align: center;
}

.login .hspLogo img {
  width: 1.92rem;
  height: 1.92rem;
  // display: inline;
}

.textinfo {
  width: 70%;
  height: 6rem;
  margin: 0 auto;
  padding-top: 0.3rem;
}

.TeamInfo {
  display: flex;
  justify-content: center;
  padding-top: 0.1rem;
}

.textimg {
  display: flex;
  /* justify-content: center; */
  align-items: center;
  width: 20%;
  height: 1.1rem;
}

.textimg img {
  width: 59%;
  height: 0.6rem;
  padding-top: 0.23rem;
}

.textlnc {
  display: flex;
  width: 80%;
  height: 1rem;
  /* text-align: center; */
  /* margin: 0 auto; */
  justify-content: center;
  align-items: center;
  border-bottom: solid 1px #a9a0a0;
}

.textlnc input {
  width: 100%;
  height: 0.8rem;
  font-size: 14px;
  border: none;
  padding-top: 0.12rem;
}

.van-button--normal {
  width: 80%;
}

.formBtn {
  width: 100%;
  border: none;
  outline: none;
  background-image: linear-gradient(-179deg, #07a8d9 0%, #018bf0 100%);
  border-radius: 0.08rem;
  color: #fff;
  font-size: 0.32rem;
  text-align: center;
  height: 0.88rem;
  line-height: 0.88rem;
  margin-top: 0.62rem;
}

.bottomtel {
  width: 90%;
  font-size: 14px;
  margin: 0 auto;
  text-align: center;
  line-height: 0.5rem;
  color: #939393;
}

.TeamLnc {
  display: flex;
  height: 1rem;
  width: 100%;
  justify-content: center;
  align-items: center;
  border-bottom: 1px solid #bcb4b4;
}

.TeamList input {
  height: 0.5rem;
  width: 100%;
  margin-left: 15px;
  margin-top: 1px;
  border: none;
  font-size: 18px;
  color: #999;
  -webkit-text-fill-color: #999;
}

.TeamIdcard {
  display: flex;
  height: 1rem;
  width: 100%;
  justify-content: center;
  align-items: center;
  border-bottom: 1px solid #bcb4b4;
  margin-top: 0.25rem;
}

.TeamIdcard input {
  height: 0.5rem;
  width: 100%;
  margin-left: 15px;
  margin-top: 1px;
  border: none;
  font-size: 18px;
  color: #999;
  -webkit-text-fill-color: #999;
}

.TeamTel {
  display: flex;
  height: 1rem;
  width: 100%;
  justify-content: center;
  align-items: center;
  border-bottom: 1px solid #bcb4b4;
  margin-top: 0.25rem;
}

.TeamTel input {
  height: 0.5rem;
  width: 100%;
  margin-left: 15px;
  margin-top: 1px;
  border: none;
  font-size: 18px;
  color: #999;
  -webkit-text-fill-color: #999;
}

.van-button--large {
  border-radius: 0.32rem !important;
  font-size: 18px !important;
  letter-spacing: 11px;
}

//遮罩层
.van-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}</style>