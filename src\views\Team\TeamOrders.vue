<template>
  <div>
    <div class="TeamOrder">
      <!-- top -->
      <div class="Information">
        <div class="InformationBox">
          <div class="InformationDiv">
            <div class="InformationTitle">
              <b>体检人信息</b>
            </div>
            <div class="InformationInput"></div>
          </div>
          <div class="InformationDiv">
            <div class="InformationTitle">
              <span>姓名</span>
            </div>
            <div class="InformationInput">
              <input type="text" placeholder="姓名" v-model="InputNames" maxlength="6" readonly />
            </div>
          </div>
          <div class="InformationDiv">
            <div class="InformationTitle">
              <span>证件号</span>
            </div>
            <div class="InformationInput">
              <input type="text" placeholder="身份证件号码" v-model="InputCards" maxlength="18" readonly />
            </div>
          </div>
          <div class="InformationDiv">
            <div class="InformationTitle">
              <span>手机号</span>
            </div>
            <div class="InformationInput">
              <input type="text" placeholder="手机号码" v-model="InputTels" maxlength="11" readonly />
            </div>
          </div>
          <div class="InformationDiv">
            <div class="InformationTitle">
              <span>体检单位</span>
            </div>
            <div class="InformationInput">
              <input type="text" placeholder="单位" v-model="lncName" maxlength="11" readonly />
            </div>
          </div>
        </div>
      </div>

      <!-- middle -->
      <div class="time">
        <div class="timeDiv">
          <div class="timeTitle">
            <span>体检时间</span>
          </div>
          <div class="timeChoice">
            <span>{{ TeamDay.date }}（{{ week }}）</span>
          </div>
        </div>
        <div class="timeDiv">
          <div class="timeTitle">
            <span>报到时间</span>
          </div>
          <div class="timeChoice">
            <span>{{ TeamDay.sumtime_Name }}</span>
          </div>
        </div>
      </div>
      <div class="SetMealNow">
        <div class="NowBox">
          <div class="SetTitle">
            <div class="SetImg">
              <img src="../../assets/detailsSex01.png" alt />
            </div>
            <div class="SetText">
              <span>{{ TeamDay.clus_name }}</span>
            </div>
          </div>
          <!-- <div class="MealText">
                    <span>适用于入职单位要求按公务员标准体检项目的进行入职体检的用户。如公司要求使用专用体检表，请自在前台办理签到</span>
          </div>-->
        </div>
      </div>
      <!-- foot -->
      <div class="confirm" @click="ToSuccess">
        <span>立即预约</span>
      </div>
      <!--遮罩层-->
      <van-overlay :show="show" v-show="show">
        <div class="vanoverBtn">
          <van-loading type="spinner" color="#1989fa">预约中...</van-loading>
        </div>
      </van-overlay>
    </div>
  </div>
</template>
<script>
import { ajax, storage, dataUtils } from "../../common";
import apiUrls from "../../config/apiUrls";
import { Toast } from "vant";
import Vue from "vue";

export default {
  data() {
    return {
      TeamDay: [],
      clus_name: "",
      InputNames: "",
      InputCards: "",
      InputTels: "",
      chooseItemTeam:[],
      clus_code: "34",
      week: "",
      lncName: "",
      show: false,
      chooseItem: {
        total: 0.0,
        chooseCombCode: ""
      }, //加减项选择的项目和总价对象
      //判断此版本是否需要加项功能
      IsAddClusItem: Vue.prototype.baseData.IsAddClusItem || false
    };
  },
  created() {
    var info = JSON.parse(storage.session.get("teamInfo"));
    this.InputNames = info[0].pat_name;
    this.InputCards = info[0].idcard;
    this.InputTels = info[0].tel;
    this.lncName = JSON.parse(storage.session.get("lncInfo")).lnc_name;
    this.TeamDay = JSON.parse(storage.session.get("TeamDay"));
    this.chooseItem = JSON.parse(storage.session.get("chooseItem")); //获取加减项选择的项目和总价对象
    this.chooseItemTeam =JSON.parse(storage.session.get("chooseItemTeam")) 
    // console.log(this.chooseItem);
    var weekDay = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
    this.week = weekDay[this.TeamDay.week];
  },
  methods: {
     ToSuccess() {

        // 表单验证
        if (!this.InputNames || !this.InputCards || !this.InputTels) {
          Toast('体检信息不能为空！');
          return;
        }

        // 准备请求数据
        console.log(this.chooseItemTeam)
         var choose_comb_code=[]
        if(this.chooseItemTeam !=null){
         choose_comb_code = this.chooseItemTeam.clusOut.map(r => r.comb_code.trim());
        }
        
        const pData = {
          openid: storage.cookie.get('openid'),
          name: this.InputNames,
          tel: this.InputTels,
          idcard: this.InputCards,
          clus_code: this.TeamDay.clus_code,
          clus_name: this.TeamDay.clus_name,
          begin_time: this.TeamDay.date,
          lnc_Code: this.TeamDay.lnccode,
          sumtime_Code: this.TeamDay.sumtime_Code,
          sumtime_Name: this.TeamDay.sumtime_Name,
          company_Name: this.lncName,
          regno: this.TeamDay.reg_no,
          choose_comb_code:!this.chooseItemTeam? "": choose_comb_code.join(','),
          price:!this.chooseItemTeam? "":this.chooseItemTeam.totalPrice,
          spType: this.TeamDay.spType,
          questionZC: storage.session.get('questionInfoZC')
        };

        // 显示加载状态
        this.show = true;

        // 发送请求
         ajax.post(apiUrls.TeamOrderAdd, pData, { nocrypt: true }).then(r=>{         
            let  response=r.data
          if(response.success){
               // 保存订单信息
          storage.session.set('OrderList', JSON.stringify(response.returnData.ord));
          storage.cookie.set('user', JSON.stringify(response.returnData.uid));
          storage.session.delete('questionInfoZC');
          
          Toast(response.returnMsg);

                 
          // 使用 Promise 处理延迟跳转
            if(this.chooseItemTeam !=null){  setTimeout(()=>{
            this.$router.push({ path: '/TeamPayment' });
           }, 2000);
          }else{
            setTimeout(()=>{
            this.$router.push({ path: '/Index' });
           }, 2000);
          }
         
          }else{
            Toast(response.returnMsg);
            this.show = false;
          }
         
          this.show = false;
        });
        
        
    
    }
  }
};
</script>
<style lang="scss" scoped>
.TeamOrder .Information {
  width: 100%;
  height: 4.33rem;
  background: white;
  position: relative;
  top: 0;
  left: 0;
  box-sizing: border-box;
}

.Information .InformationBox {
  width: 92%;
  height: 100%;
  margin: 0 auto;
  /* border: 1px solid; */
}

.InformationBox .InformationDiv {
  width: 100%;
  height: 0.87rem;
  display: flex;
  border-bottom: 1px solid #dfe3e9;
  box-sizing: border-box;
  font-size: 0.28rem;
}

.InformationDiv .InformationTitle {
  width: 30%;
  height: 100%;
  display: flex;
  align-items: center;
  color: #4a4a4a;
}

.InformationDiv .InformationInput {
  width: 70%;
  height: 100%;
  display: flex;
  color: #9b9b9b;
}

.InformationInput input {
  width: 100%;
  // height: 100%;
  border: none;
  outline: medium;
  text-align: right;
}

.TeamOrder .time {
  width: 100%;
  height: 1.74rem;
  background: white;
  margin-top: 0.16rem;
}

.time .timeDiv {
  width: 92%;
  height: 0.87rem;
  margin: 0 auto;
  box-sizing: border-box;
  font-size: 0.28rem;
  display: flex;
  border-bottom: 1px solid #dfe3e9;
}

.timeDiv .timeTitle {
  width: 30%;
  height: 100%;
  display: flex;
  align-items: center;
  color: #4a4a4a;
}

.timeDiv .timeChoice {
  width: 70%;
  height: 100%;
  color: #9b9b9b;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.timeChoice .priceChoice {
  font-size: 0.24rem;
  color: #d0021b;
  letter-spacing: -0.02px;
}

.timeChoice span:nth-child(2) {
  font-size: 0.4rem;
  color: #d0021b;
  letter-spacing: -0.02px;
  line-height: 0.36rem;
}

/* 套餐 */
.TeamOrder .SetMealNow {
  width: 100%;
  /* height: 2.66rem; */
  background: white;
  margin-top: 0.16rem;
  padding: 0.36rem 0;
}

.SetMealNow .NowBox {
  width: 92%;
  height: 100%;
  margin: 0 auto;
  box-sizing: border-box;
  font-size: 0.28rem;
}

.NowBox .SetTitle {
  width: 100%;
  height: 1rem;
  display: flex;
}

.SetTitle .SetImg {
  width: 0.75rem;
  height: 1rem;
  display: flex;
  // justify-content: start;
  align-items: center;
}

.SetImg img {
  margin-top: 3px;
}

.SetTitle .SetText {
  font-size: 0.32rem;
  color: #4a4a4a;
  letter-spacing: -0.02px;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.NowBox .MealText {
  width: 100%;
  min-height: 1.3rem;
  max-height: 3.5rem;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #9b9b9b;
  letter-spacing: -0.01px;
  line-height: 0.44rem;
  overflow-y: scroll;
}

/* 跳转按钮 */
.confirm {
  width: 92%;
  height: 0.96rem;
  background: #6a9be4;
  border-radius: 5px;
  position: fixed;
  bottom: 0.36rem;
  left: 4%;
  font-size: 0.32rem;
  color: #ffffff;
  letter-spacing: -0.02px;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 消息提示 */
.text-tip {
  display: block;
  background: rgba(0, 0, 0, 0.7);
  color: #fff;
  padding: 15px 15px;
  line-height: 18px;
  position: fixed;
  left: 50%;
  bottom: 55%;
  -webkit-transform: translate(-50%);
  transform: translate(-50%);
  border-radius: 3px;
  display: none;
  z-index: 9999;
  font-size: 14px;
  text-align: center;
}

.vanoverBtn {
  display: flex;
  width: 50%;
  height: 1rem;
  background-color: #fdfdfd;
  justify-content: center;
  align-items: center;
  border-radius: 30px;
}
</style>