<template>
    <div>   
        <div id="wrap">
        <div class="success-page">
            <!-- head -->
            <div class="success-head">
                <!-- <img src="~/Content/img/chongzhichenggong.png" alt=""> -->
                <img src="../../assets/PaySuccess.png" alt="">
                <h3>预订成功</h3>
                <!-- <div v-if="isUsePay==true" style="display: flex;justify-content: center;margin-top: 0.15rem;font-size: 12px;width: 90%;margin: auto;line-height: 20px;color: brown">                   
                    <van-count-down
                        :time="time"
                        format="已预订成功,请在mm 分 ss 秒 内完成支付，否则系统将自动取消本次交易"
                        @finish="finish"/>                  
                </div> -->
            </div>
            <!-- content -->
            <div class="success-content">
                <div class="list-wrap">
                    <p>体检用户:</p>
                    <span>{{OrderSt.name}}</span>
                </div>
                <div class="list-wrap">
                    <p>体检日期:</p>
                    <span>{{OrderSt.begin_Time.substr(0,10)}}</span>
                </div>
                <div class="list-wrap">
                    <p>体检套餐:</p>
                    <span>{{OrderSt.clus_Name}}</span>
                </div>
                <div class="list-wrap">
                    <p>下单时间:</p>
                    <span>{{created_time}}</span>
                </div>
                <div class="list-wrap">
                    <p>加项金额:</p>
                    <span>￥{{addPrice}}</span>
                </div>
            </div>
            <!-- button -->
            <div class="footer-btn" v-if="comfimPay==true">
                <button @click="lookOrder()"> 查看订单</button>
                <button @click="PayAmoutOnlick()" v-if="isUsePay==true" >确定支付</button>
            </div>
            <div class="past" v-else>
                <span >订单已过期</span>
            </div>
            <div class="tip">
                <p v-if="isShowNote" style="width: 96%;margin: 0 auto;">预约成功后如需取消订单，请于体检前一天16:30分登录微信进行操作</p>
            </div>
        </div>
    </div>
    </div>
</template>
<script>
import { storage,ajax } from '../../common';
import {Toast} from 'vant';
import apiUrls from '../../config/apiUrls';
import wx from 'weixin-jsapi';
import Vue from 'vue';

export default {
    data(){
        return{
            isShowNote: true,
            OrderSt: [],
            begin_time: "",
            cancelTime:"",
            created_time:'',
            name: "",
            PayToacl: "",
            time:'',
            addPrice:'',
            comfimPay:true,
            isUsePay:Vue.prototype.baseData.IsUsePay||false
        }       
    },
    created(){
        storage.session.delete("questionInfoZC");
        // storage.session.delete("questionInfoZCA");
        var user=JSON.parse(storage.cookie.get("user"));
        this.OrderSt=JSON.parse(storage.session.get('OrderList'));
        var creaTime=new Date(this.OrderSt.created_Time);
        var creatTime=creaTime.getFullYear()+"/"+((creaTime.getMonth()+1)<10?"0"+(creaTime.getMonth()+1):creaTime.getMonth()+1)+"/"+creaTime.getDate()+" "+creaTime.getHours()+":"+creaTime.getMinutes()+":"+creaTime.getSeconds();
        this.created_time=creatTime;
         var day=new Date();
        var TodyTime=day.getFullYear()+"/"+((day.getMonth()+1)<10?"0"+(day.getMonth()+1):day.getMonth()+1)+"/"+day.getDate()+" "+day.getHours()+":"+day.getMinutes()+":"+day.getSeconds();
        var num=Date.parse(TodyTime)-Date.parse(creatTime);
        if(num>=1800000){
            this.time=0;
        }
        else{
            this.time=1800000-num;
        }

        this.addPrice  =JSON.parse(storage.session.get("chooseItemTeam")).totalPrice
    },
    methods:{
        finish(){
            this.comfimPay=false;
            return;
        },
        syncOrderById(){
            let pData={
                id:this.OrderSt.id,
                idCard:this.OrderSt.idCard
            }

            ajax.post(apiUrls.SyncOrderById,pData,{nocrypt:true}).then(r=>{

                Toast(r?"同步成功":"同步失败")
            })
        },
        PayAmoutOnlick(){
            var that=this;
            var pData=that.OrderSt;
            console.log(pData)
    
            ajax.post(apiUrls.PersonOrderPay,pData,{nocrypt:true}).then(r=>{
                var data=r.data.returnData;
                 WeixinJSBridge.invoke('getBrandWCPayRequest', {
                    "appId": data.appid, //公众号名称，由商户传入
                    "timeStamp":data.timeStamp, //时间戳
                    "nonceStr": data.nonceStr, //随机串
                    "package": data.package,//扩展包
                    "signType": "MD5", //微信签名方式:MD5
                    "paySign": data.paySign //微信签名
                }, function (res) {
                    switch(res.err_msg){
                        case 'get_brand_wcpay_request:cancel':
                            alert("取消支付");       
                            that.$router.replace('/MyOrderList')                                              
                            break; 
                        case 'get_brand_wcpay_request:fail': 
                            alert("支付失败，可能的原因：签名错误、未注册APPID、项目设置APPID不正确、注册的APPID与设置的不匹配、其他异常等。"); 
                            break; 
                        case 'get_brand_wcpay_request:ok':
                        that.syncOrderById()
                              that("支付成功");
                             that.$router.replace('/MyOrderList')         
                            break; 
                    }                                    
                });              
            }).catch(e=>{
                alert("系统繁忙！请稍后再试");
                return;
            })
        },
        lookOrder(){
            this.$router.replace({
                path:'/MyOrderList'
            })
        }
        // wxcofig(){
        //     // var url= "http://hlh.krmanager.com";
        //     var url= window.location.href;
        //     ajax.get(apiUrls.getWxConfig,{params:{url:url}}).then(r=>{
        //         var data=r.data.returnData;
        //         wx.config({
        //             debug:true,
        //             appId:"wxabbe0c4a5776c1e7",   
        //             timestamp: data.timeStamp, 
        //             nonceStr: data.nonceStr,    
        //             signature: data.signature,                     
        //         })
        //     }).catch(e=>{               
        //         alert(e);
        //     })
        // },
    }
}
</script>
<style lang="scss">
        .past{
            width: 80%;
            background-color: #939aa2;
            text-align: center;
            margin: 0 auto;
            display: flex;
            height: 1rem;
            justify-content: center;
            align-items: center;
            border-radius: 0.2rem;
            margin-top: 0.3rem;
        }
        .past span{
        font-size: 22px;color: #fff;
        }
        .success-page {
           padding: 0.2rem 0;
           background: #f0eff6;
           height: 100%;
           width: 100%;
           overflow: auto;
       }

       .success-head {
           padding: 0.36rem 0 0.44rem 0;
       }

           .success-head img {
               width: 1.52rem;
               height: 1.52rem;
               display: block;
               margin: 0 auto 0.2rem;
           }

           .success-head h3 {
               text-align: center;
               font-size: 0.4rem;
               color: #354052;
               font-weight: normal;
           }

       .success-content {
           width: 96%;
           background: #fff;
           border-radius: 5px;
           padding: 0.4rem 0;
           margin-bottom: 0.48rem;
           margin: 0 auto;
       }

           .success-content .list-wrap {
               display: flex;
               height: 0.44rem;
               align-items: center;
               font-size: 0.32rem;
               color: #7f8fa4;
               overflow: hidden;
               margin-bottom: 0.24rem;
               padding-left: .28rem;
           }

               .success-content .list-wrap:last-child {
                   margin-bottom: 0;
               }

               .success-content .list-wrap span {
                   color: #354052;
                   flex-grow: 1;
                   /*text-align: right;*/
               }
       .footer-btn {
           display: flex;
           justify-content: space-around;
           margin-top: .5rem;
       }
       .footer-btn button {
           width: 40%;
           height: 1.08rem;
           border-radius: 5px;
           background: #80b1eb;
           border: 1px solid #80b1eb;
           display: block;
           font-size: 0.36rem;
           color: #ffffff;
       }

           .footer-btn button a {
               color: #ffffff;
           }

       .tip {
           /* margin-bottom: 1rem; */
           margin-top: .2rem;
           color: red;
           font-size: .3rem;
           justify-content: center;
       }
</style>