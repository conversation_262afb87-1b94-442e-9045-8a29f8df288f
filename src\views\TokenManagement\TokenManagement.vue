<template>
  <div class="token-management">
    <div class="header">
      <div class="back-button" @click="goBack">
        <van-icon name="arrow-left" size="20" />
      </div>
      <div class="title">代币管理</div>
    </div>

    <div class="content">
      <div v-if="loading" class="loading-container">
        <van-loading type="spinner" color="#1989fa" />
        <div class="loading-text">加载中...</div>
      </div>

      <div v-else-if="tokenList.length === 0" class="empty-container">
        <div class="empty-icon">
          <van-icon name="info-o" size="48" />
        </div>
        <div class="empty-text">暂无代币记录</div>
      </div>

      <div v-else class="token-list">
        <div v-for="(item, index) in tokenList" :key="index" class="token-item" @click="viewTokenDetail(item)">
          <div class="token-item-header">
            <div class="token-item-title">代币卡号：{{ item.card_no }}</div>
            <!-- <div class="token-item-status" :class="getStatusClass(item.status)">{{ getStatusText(item.status) }}</div> -->
          </div>
          <div class="token-item-content">
            <div class="token-item-info">
              <div class="info-row">
                <span class="label">代币卡号：</span>
                <span class="value">{{ item.card_no }}</span>
              </div>

              <div class="info-row">
                <span class="label">姓名：</span>
                <span class="value">{{ item.name }}</span>
              </div>
              
              <div class="info-row">
                <span class="label">金额：</span>
                <span class="value">{{ item.par_value }}</span>
              </div>

               <div class="info-row">
                <span class="label">工号：</span>
                <span class="value">{{ item.oper }}</span>
              </div>
              
            

              <div class="info-row">
                <span class="label">申请时间：</span>
                <span class="value">{{ item.ddrq }}</span>
              </div>
            </div>
          </div>
          <div class="token-item-footer">
            <van-button size="small" type="primary" @click.stop="updateTokenStatus(item, 'T')">批准</van-button>
            <van-button size="small" type="danger" @click.stop="updateTokenStatus(item, 'F')">拒绝</van-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 登录验证弹窗 -->
    <van-action-sheet v-model="tokenManagementShow" :close-on-click-overlay="false" :closeable="false" title="登录">
      <div class="token-management-login" style="background: #fff;">
        <!-- 密码输入框 -->
        <van-password-input
          :value="tokenManagementValue"
          :mask="false"
          :focused="showKeyboard"
          @focus="showKeyboard = true"
        />
        <!-- 数字键盘 -->
        <van-number-keyboard v-model="tokenManagementValue" :show="showKeyboard" />
      </div>
    </van-action-sheet>
  </div>
</template>

<script>
import { Toast } from "vant";
import { ajax, storage } from "../../common";
import apiUrls from "../../config/apiUrls";

export default {
  name: "TokenManagement",
  data() {
    return {
      openid: "",
      pws: "",
      tokenManagementValue: '',
      tokenManagementShow: true,
      showKeyboard: true,
      loading: true,
      tokenList: []
    };
  },
  watch: {
    tokenManagementValue(value) {
      if (value.length === 6) {
        this.verifyTokenManagementPwd(value);
      } else {
        this.errorInfo = '';
      }
    }
  },
  created() {
    // 模拟API加载
    setTimeout(() => {
      this.loading = false;
    }, 1000);

    // 实际项目中应该调用API获取数据
    this.getTokenList();
  },
  methods: {
    verifyTokenManagementPwd(tokenManagementValue) {
      var pData = {
        id: 1,
        pwd: tokenManagementValue
      }
      ajax
        .post(apiUrls.VerifyDirectorReviewPwd, pData)
        .then(response => {
          console.log(response.data.returnData)
          if (response.data.returnData) {
            Toast("登陆成功！")
            this.tokenManagementShow = false;
            this.getTokenList();
          } else {
            this.errorInfo = '密码错误';
            Toast("密码错误")
          }
          return response.data.returnData
        })
        .catch(error => {
          Toast("获取代币列表失败，请稍后重试");
          this.loading = false;
        });
    },
    goBack() {
      this.$router.go(-1);
    },
    getTokenList() {
      this.loading = true;
      // 调用获取代币列表的API
      ajax
        .post(apiUrls.GetCardPerson, {})
        .then(response => {
          this.tokenList = JSON.parse(response.data.returnData);
          this.loading = false;
        })
        .catch(error => {
          Toast("获取代币列表失败，请稍后重试");
          this.loading = false;
        });
    },
    formatDate(dateString) {
      if (!dateString) return "";
      return dateString;
    },
    getStatusText(status) {
      const statusMap = {
        "0": "待处理",
        "1": "已批准",
        "2": "已拒绝"
      };
      return statusMap[status] || "未知状态";
    },
    getStatusClass(status) {
      return `status-${status}`;
    },
    viewTokenDetail(item) {
      // 查看详情逻辑
      Toast(`查看详情: ${item.patName}`);
    },
    updateTokenStatus(item, status) {
      var pData = {
        idCard: item.card_no,
        name: item.oper,
        status:status
      }

      ajax
        .post(apiUrls.UpdateCardPerson, pData, { nocrypt: true }).then(r => {
          this.getTokenList()
        })

      // 更新状态逻辑
      const statusText = status === 'T' ? '批准' : '拒绝';
      Toast(`${statusText}成功`);
    }
  }
};
</script>

<style lang="scss" scoped>
.token-management-login {
  height: 100vh;
}

.token-management {
  width: 100%;
  min-height: 100vh;
  background-color: #f7f8fa;
}

.header {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 46px;
  background-color: #fff;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.back-button {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
}

.title {
  font-size: 18px;
  font-weight: 500;
  color: #323233;
}

.content {
  padding: 16px;
}

.loading-container,
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
}

.loading-text,
.empty-text {
  margin-top: 12px;
  font-size: 14px;
  color: #969799;
}

.token-list {
  margin-bottom: 20px;
}

.token-item {
  margin-bottom: 12px;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.token-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f2f3f5;
}

.token-item-title {
  font-size: 16px;
  font-weight: 500;
  color: #323233;
}

.token-item-status {
  font-size: 14px;
  padding: 2px 8px;
  border-radius: 4px;
}

.status-0 {
  color: #ff976a;
  background-color: #fff7e8;
}

.status-1 {
  color: #07c160;
  background-color: #e8f7ef;
}

.status-2 {
  color: #ee0a24;
  background-color: #fef0f0;
}

.token-item-content {
  padding: 12px 16px;
}

.token-item-info {
  font-size: 14px;
}

.info-row {
  display: flex;
  margin-bottom: 8px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.label {
  color: #969799;
  width: 80px;
}

.value {
  color: #323233;
  flex: 1;
}

.token-item-footer {
  display: flex;
  justify-content: flex-end;
  padding: 12px 16px;
  border-top: 1px solid #f2f3f5;
}

.token-item-footer .van-button {
  margin-left: 8px;
}
</style>
