<template>
  <!--授权登录-->
  <!-- <div class="oauthtitle" >
        {{title}}
    </div> -->
  <div class="intersecting-circles-spinner">
    <div class="spinnerBlock">
      <span class="circle"></span>
      <span class="circle"></span>
      <span class="circle"></span>
      <span class="circle"></span>
      <span class="circle"></span>
      <span class="circle"></span>
      <span class="circle"></span>
    </div>
  </div>
</template>

<script>
import { storage, ajax, unique } from '../common'
import { authService } from '../service/index'
import apiUrls from '../config/apiUrls'
import { Toast } from 'vant'
import Vue from "vue";

export default {
  components: {
    // HeaderBar
  },
  data() {
    return {
      title: "正在授权中...",
      success: '',
      type: '',
      openid: ''
    }
  },
  created() {

  },
  mounted() {
    //是否使用isunique
    // if(Vue.prototype.baseData.isunique==true)
    // {
    //     this.openid=unique.getunique();
    //     if(this.openid!='' && this.openid!=null){
    //     this.type="outopenid";           
    //     return this.weChatAuthorized(this.openid);
    //     }
    // }
    //第三方授权开启
    // if (Vue.prototype.baseData.otherOpenid == true) {
    //   this.openid = this.$route.query['openid'];
    //   if (this.openid != '' && this.openid != null) {
    //     this.type = "outopenid";
    //     return this.weChatAuthorized(this.openid);
    //   }
    // }

    //
    if (this.$route.query['repage']) {
      storage.session.set('repage', this.$route.query['repage']);
    }

    this.success = this.$route.query['success'];
    this.type = this.$route.query['type'] || 'login';
    if (this.type == "OAuthCallback" || this.success == 0) {
      this.title = this.$route.query['returnMsg'];
      Toast(this.title);
      return;
    }
    if (this.success == undefined || this.success == '' || this.success != 1) {
      storage.session.set('oauthurl', this.$route.query['oauthurl']);
      storage.session.set('pcid', this.$route.query['pcid']);
      this.toWeChatOauth();
      return;
    }
    this.weChatAuthorized();
  },
  methods: {

    //微信授权登录
    toWeChatOauth() {
      var that = this;
      //第三方授权开启
      // if (Vue.prototype.baseData.otherOpenid == true) {
      //   window.location.replace(Vue.prototype.baseData.otherOpenidUrl);
      //   return;
      // }

      ajax.get(apiUrls.toWeChatOauth + '?targetUrl=' + this.type).then(r => {
        r = r.data;
        if (r.success == false) {
          that.title = "跳转微信授权";
          window.location.replace(r.returnData);
          return true;
        }
        that.title = "微信授权成功！";
        return that.weChatAuthorized(r.returnData);

      }).catch(err => {
        console.error(err);
        Toast("微信授权失败");
        return false;
      })
    },

    //获取到的openid做的业务
    weChatAuthorized(nopenid) {
      //获取用户信息
      if (nopenid != null && nopenid != undefined && nopenid != '') {
        this.openid = nopenid;
      } else {
        this.openid = this.$route.query['returnData'];
      }
      storage.cookie.set("openid", this.openid);
      // var tourl=storage.session.get('redirect')||'/';
      // //openid登录方法去做授权获取user
      // switch (this.type) {
      //     case 'wxhub':
      //         this.$router.replace({ path: tourl });
      //         break;
      //         //第三方授权使用
      //     case 'outopenid':
      //          this.openidlogin(this.openid,tourl);
      //         break;
      //     case 'jump':
      //         {
      //             this.openidlogin(this.openid,tourl);
      //             break;
      //         }
      //     default:
      //         this.$router.replace({ path: "/" });
      //         break;
      // }
      if (storage.session.get('token') != "" && storage.session.get('token') != null) {
        this.openidlogin(this.openid);
      } else {
        var that = this;
        setTimeout(function () {
          that.openidlogin(that.openid);
        }, 1500)
      }
      // this.openidlogin(this.openid);
      return;
    },
    openidlogin(openid) {
      var data = {
        openid: openid
      };
      var that = this;
      var tourl = storage.session.get('redirect') || '/';
      ajax.post(apiUrls.userOpenid, data, { nocrypt: true }).then(r => {
        r = r.data;
        if (!r.success) {
          Toast(r.returnMsg);
          if (r.returnMsg == "暂未注册") {
            this.$router.replace({ path: "/login" });
          }
          return;
        }
        // storage.cookie.set('user', JSON.stringify(r.returnData));
        authService.setToken(r.returnData.tokenInfo); 
        storage.cookie.set('user', JSON.stringify(r.returnData.user));
        storage.cookie.set('isOauth', "1");
        // if (storage.session.get("repage")) {
        //   window.location.href = "http://tijian.chinadlhospital.com/#/" + storage.session.get("repage");
        //   return;
        // }
        
        if (that.type == "outopenid") {
          this.$router.replace({ path: tourl });
        } else {
          this.$router.go(-2);
        }
        return;
      }).catch(e => {
        console.log(e);
        Toast('网络繁忙:' + JSON.stringify(e));
        this.$router.go(-2);
        return;
      })
    }

  }

};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.oauthtitle {
  font-size: x-large;
}

.intersecting-circles-spinner,
.intersecting-circles-spinner * {
  box-sizing: border-box;
}

.intersecting-circles-spinner {
  top: 20vh;
  position: relative;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}

.intersecting-circles-spinner .spinnerBlock {
  animation: intersecting-circles-spinners-animation 1200ms linear infinite;
  transform-origin: center;
  display: block;
  height: 35px;
  width: 35px;
}

.intersecting-circles-spinner .circle {
  display: block;
  border: 2px solid #676062;
  border-radius: 50%;
  height: 100%;
  width: 100%;
  position: absolute;
  left: 0;
  top: 0;
}

.intersecting-circles-spinner .circle:nth-child(1) {
  left: 0;
  top: 0;
}

.intersecting-circles-spinner .circle:nth-child(2) {
  left: calc(35px * -0.36);
  top: calc(35px * 0.2);
}

.intersecting-circles-spinner .circle:nth-child(3) {
  left: calc(35px * -0.36);
  top: calc(35px * -0.2);
}

.intersecting-circles-spinner .circle:nth-child(4) {
  left: 0;
  top: calc(35px * -0.36);
}

.intersecting-circles-spinner .circle:nth-child(5) {
  left: calc(35px * 0.36);
  top: calc(35px * -0.2);
}

.intersecting-circles-spinner .circle:nth-child(6) {
  left: calc(35px * 0.36);
  top: calc(35px * 0.2);
}

.intersecting-circles-spinner .circle:nth-child(7) {
  left: 0;
  top: calc(35px * 0.36);
}

@keyframes intersecting-circles-spinners-animation {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}
</style>

